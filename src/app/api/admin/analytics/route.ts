import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { jwtVerify } from "jose";

// GET /api/admin/analytics - <PERSON><PERSON><PERSON> dữ liệu thống kê
export async function GET(request: NextRequest) {
  try {
    // Verify admin session
    const adminToken = request.cookies.get("admin-session")?.value;

    if (!adminToken) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    try {
      const secret = new TextEncoder().encode(
        process.env.NEXTAUTH_SECRET || "fallback-secret"
      );
      const { payload } = await jwtVerify(adminToken, secret);

      // Check if user is admin or moderator (both can view analytics)
      if (
        !payload.role ||
        (payload.role !== "ADMIN" && payload.role !== "MODERATOR")
      ) {
        return NextResponse.json(
          { error: "Không có quyền truy cập" },
          { status: 403 }
        );
      }
    } catch {
      return NextResponse.json(
        { error: "Token không hợp lệ" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const range = searchParams.get("range") || "30d";

    // Calculate date ranges
    const now = new Date();
    let startDate: Date;
    let previousStartDate: Date;

    switch (range) {
      case "7d":
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        previousStartDate = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);
        break;
      case "90d":
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        previousStartDate = new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000);
        break;
      case "1y":
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        previousStartDate = new Date(now.getTime() - 730 * 24 * 60 * 60 * 1000);
        break;
      default: // 30d
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        previousStartDate = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);
    }

    // Get current period data
    const [currentOrders, previousOrders] = await Promise.all([
      prisma.order.findMany({
        where: {
          createdAt: {
            gte: startDate,
          },
        },
        select: {
          total: true,
          userId: true,
        },
      }),
      prisma.order.findMany({
        where: {
          createdAt: {
            gte: previousStartDate,
            lt: startDate,
          },
        },
        select: {
          total: true,
          userId: true,
        },
      }),
    ]);

    // Calculate totals
    const totalRevenue = currentOrders.reduce(
      (sum, order) => sum + order.total,
      0
    );
    const previousRevenue = previousOrders.reduce(
      (sum, order) => sum + order.total,
      0
    );
    const totalOrders = currentOrders.length;
    const previousOrdersCount = previousOrders.length;

    // Get unique customers
    const currentCustomers = new Set(currentOrders.map((o) => o.userId)).size;
    const previousCustomers = new Set(previousOrders.map((o) => o.userId)).size;

    // Get total products and customers
    const [totalProducts, totalCustomersCount] = await Promise.all([
      prisma.product.count({ where: { status: "ACTIVE" } }),
      prisma.user.count(), // All users in User model are customers
    ]);

    // Calculate growth rates
    const revenueGrowth =
      previousRevenue > 0
        ? ((totalRevenue - previousRevenue) / previousRevenue) * 100
        : 0;
    const ordersGrowth =
      previousOrdersCount > 0
        ? ((totalOrders - previousOrdersCount) / previousOrdersCount) * 100
        : 0;
    const customersGrowth =
      previousCustomers > 0
        ? ((currentCustomers - previousCustomers) / previousCustomers) * 100
        : 0;

    // Get top products
    const topProductsData = await prisma.orderItem.groupBy({
      by: ["productId"],
      _sum: {
        quantity: true,
        total: true,
      },
      orderBy: {
        _sum: {
          quantity: "desc",
        },
      },
      take: 5,
    });

    const topProducts = await Promise.all(
      topProductsData.map(async (item) => {
        const product = await prisma.product.findUnique({
          where: { id: item.productId },
          select: { id: true, name: true },
        });
        return {
          ...product,
          totalSold: item._sum.quantity || 0,
          revenue: item._sum.total || 0,
        };
      })
    );

    // Get top categories
    const topCategoriesData = await prisma.orderItem.findMany({
      include: {
        product: {
          include: {
            category: true,
          },
        },
      },
    });

    const categoryStats = topCategoriesData.reduce((acc: any, item) => {
      const categoryId = item.product.category.id;
      const categoryName = item.product.category.name;

      if (!acc[categoryId]) {
        acc[categoryId] = {
          id: categoryId,
          name: categoryName,
          revenue: 0,
          productCount: new Set(),
        };
      }

      acc[categoryId].revenue += item.total;
      acc[categoryId].productCount.add(item.productId);

      return acc;
    }, {});

    const topCategories = Object.values(categoryStats)
      .map((cat: any) => ({
        ...cat,
        productCount: cat.productCount.size,
      }))
      .sort((a: any, b: any) => b.revenue - a.revenue)
      .slice(0, 5);

    // Get orders by status
    const ordersByStatus = await prisma.order.groupBy({
      by: ["status"],
      _count: {
        status: true,
      },
    });

    const totalOrdersForStatus = ordersByStatus.reduce(
      (sum, item) => sum + item._count.status,
      0
    );
    const ordersStatusDistribution = ordersByStatus.map((item) => ({
      status: item.status,
      count: item._count.status,
      percentage:
        totalOrdersForStatus > 0
          ? (item._count.status / totalOrdersForStatus) * 100
          : 0,
    }));

    // Customer stats
    const allCustomers = await prisma.user.findMany({
      // All users in User model are customers
      include: {
        orders: {
          select: {
            total: true,
            createdAt: true,
          },
        },
      },
    });

    const newCustomers = allCustomers.filter(
      (customer) =>
        customer.orders.length === 1 &&
        customer.orders[0].createdAt >= startDate
    ).length;

    const returningCustomers = allCustomers.filter(
      (customer) => customer.orders.length > 1
    ).length;

    const allOrders = await prisma.order.findMany({
      select: { total: true },
    });

    const averageOrderValue =
      allOrders.length > 0
        ? allOrders.reduce((sum, order) => sum + order.total, 0) /
          allOrders.length
        : 0;

    // Get monthly revenue data for charts
    const monthlyRevenueData = await prisma.order.groupBy({
      by: ["createdAt"],
      where: {
        createdAt: {
          gte: new Date(now.getFullYear(), now.getMonth() - 11, 1), // Last 12 months
        },
        status: {
          in: ["COMPLETED" as const, "DELIVERED" as const],
        },
      },
      _sum: {
        total: true,
      },
      _count: {
        id: true,
      },
    });

    // Process monthly data
    const monthlyRevenue = [];
    for (let i = 11; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);

      const monthData = monthlyRevenueData.filter((item) => {
        const itemDate = new Date(item.createdAt);
        return (
          itemDate.getFullYear() === date.getFullYear() &&
          itemDate.getMonth() === date.getMonth()
        );
      });

      const monthRevenue = monthData.reduce(
        (sum, item) => sum + (item._sum?.total || 0),
        0
      );
      const monthOrders = monthData.length;

      monthlyRevenue.push({
        month: date.toLocaleDateString("vi-VN", {
          month: "short",
          year: "numeric",
        }),
        revenue: monthRevenue,
        orders: monthOrders,
      });
    }

    const analyticsData = {
      overview: {
        totalRevenue,
        totalOrders,
        totalCustomers: totalCustomersCount,
        totalProducts,
        revenueGrowth,
        ordersGrowth,
        customersGrowth,
      },
      monthlyRevenue,
      topProducts: topProducts.filter((p) => p.id),
      topCategories,
      ordersByStatus: ordersStatusDistribution,
      customerStats: {
        newCustomers,
        returningCustomers,
        averageOrderValue,
      },
    };

    return NextResponse.json(analyticsData);
  } catch (error) {
    console.error("Get analytics error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy dữ liệu thống kê" },
      { status: 500 }
    );
  }
}
