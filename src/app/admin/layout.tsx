"use client";

import { usePathname } from "next/navigation";
import AdminLayoutWithSidebar from "./admin-layout-with-sidebar";
import AdminAuthGuard from "@/components/admin/admin-auth-guard";

export default function AdminLayoutPage({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  // Check if current path is auth route
  const isAuthRoute = pathname?.startsWith("/admin/auth");

  // If it's an auth route, don't wrap with AdminAuthGuard
  if (isAuthRoute) {
    return <>{children}</>;
  }

  // For all other admin routes, wrap with auth guard and sidebar layout
  return (
    <AdminAuthGuard>
      <AdminLayoutWithSidebar>{children}</AdminLayoutWithSidebar>
    </AdminAuthGuard>
  );
}
