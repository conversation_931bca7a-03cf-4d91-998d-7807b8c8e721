import { test, expect } from "@playwright/test";

test.describe("Admin Authentication Guard", () => {
  test("Should redirect to signin when accessing /admin without authentication", async ({
    page,
  }) => {
    // Truy cập trực tiếp admin dashboard mà không đăng nhập
    await page.goto("/admin");

    // Đợi redirect
    await page.waitForTimeout(2000);

    // Kiểm tra có redirect tới signin page không
    expect(page.url()).toContain("/admin/auth/signin");

    // Kiểm tra form đăng nhập hiển thị
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
  });

  test("Should show loading state while checking authentication", async ({
    page,
  }) => {
    // Intercept API call để làm chậm response
    await page.route("/api/admin-verify", async (route) => {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      await route.fulfill({
        status: 401,
        contentType: "application/json",
        body: JSON.stringify({ error: "Not authenticated" }),
      });
    });

    await page.goto("/admin");

    // Kiểm tra loading message hiển thị
    await expect(
      page.locator("text=Đang kiểm tra quyền truy cập")
    ).toBeVisible();
  });

  test("Should allow access to admin dashboard when authenticated", async ({
    page,
  }) => {
    // Đăng nhập trước
    await page.goto("/admin/auth/signin");
    await page.fill('input[type="email"]', "<EMAIL>");
    await page.fill('input[type="password"]', "password123");
    await page.click('button[type="submit"]');

    // Đợi redirect sau login
    await page.waitForURL("/admin");

    // Kiểm tra admin dashboard hiển thị
    await expect(page.locator('[data-testid="admin-sidebar"]')).toBeVisible();
    await expect(page.locator('[data-testid="admin-header"]')).toBeVisible();
    await expect(page.locator("text=Dashboard")).toBeVisible();
  });

  test("Should not allow access with invalid token", async ({ page }) => {
    // Set invalid admin session cookie
    await page.context().addCookies([
      {
        name: "admin-session",
        value: "invalid-token",
        domain: "localhost",
        path: "/",
      },
    ]);

    await page.goto("/admin");

    // Đợi redirect
    await page.waitForTimeout(2000);

    // Kiểm tra redirect tới signin
    expect(page.url()).toContain("/admin/auth/signin");
  });

  test("Should preserve redirect URL after login", async ({ page }) => {
    // Try to access specific admin page without auth
    await page.goto("/admin/products");

    // Should redirect to signin
    await page.waitForTimeout(2000);
    expect(page.url()).toContain("/admin/auth/signin");

    // Login
    await page.fill('input[type="email"]', "<EMAIL>");
    await page.fill('input[type="password"]', "password123");
    await page.click('button[type="submit"]');

    // Should redirect back to originally requested page or admin dashboard
    await page.waitForURL("/admin");

    // Verify we're in admin area
    await expect(page.locator('[data-testid="admin-sidebar"]')).toBeVisible();
  });
});
