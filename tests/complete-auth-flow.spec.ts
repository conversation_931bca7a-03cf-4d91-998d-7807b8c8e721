import { test, expect } from "@playwright/test";

test.describe("Complete Admin Authentication Flow", () => {
  test("Full authentication protection and access flow", async ({ page }) => {
    console.log("=== Testing Complete Admin Auth Flow ===");

    // Step 1: Clear cookies to ensure no existing session
    await page.context().clearCookies();
    console.log("✓ Cleared all cookies");

    // Step 2: Try to access admin dashboard without login
    console.log("→ Attempting to access /admin without authentication...");
    await page.goto("/admin");

    // Wait for auth guard to process
    await page.waitForTimeout(3000);

    const currentUrl = page.url();
    console.log("Current URL:", currentUrl);

    // Should be redirected to signin page
    if (currentUrl.includes("/admin/auth/signin")) {
      console.log("✓ Successfully redirected to signin page");
    } else {
      console.log("❌ Not redirected to signin page");
      await page.screenshot({ path: "auth-redirect-fail.png" });
    }

    // Step 3: Login with admin credentials
    console.log("→ Logging in with admin credentials...");
    await page.goto("/admin/auth/signin");
    await page.fill('input[type="email"]', "<EMAIL>");
    await page.fill('input[type="password"]', "password123");
    await page.click('button[type="submit"]');

    // Wait for login to complete
    await page.waitForTimeout(3000);

    const afterLoginUrl = page.url();
    console.log("URL after login:", afterLoginUrl);

    // Should be redirected to admin dashboard
    if (afterLoginUrl.includes("/admin")) {
      console.log("✓ Successfully logged in and redirected to admin");

      // Verify admin dashboard elements are visible
      const sidebarVisible = await page
        .locator('[data-testid="admin-sidebar"]')
        .isVisible();
      const headerVisible = await page
        .locator('[data-testid="admin-header"]')
        .isVisible();

      console.log("Sidebar visible:", sidebarVisible);
      console.log("Header visible:", headerVisible);

      if (sidebarVisible && headerVisible) {
        console.log("✓ Admin dashboard loaded successfully");
      } else {
        console.log("❌ Admin dashboard elements not visible");
        await page.screenshot({ path: "admin-dashboard-elements.png" });
      }
    } else {
      console.log("❌ Login failed or redirect failed");
      await page.screenshot({ path: "login-redirect-fail.png" });
    }

    // Step 4: Test logout and re-protection
    console.log("→ Testing logout...");
    const logoutButton = page.locator('button:has-text("Logout")');
    if (await logoutButton.isVisible()) {
      await logoutButton.click();
      await page.waitForTimeout(2000);

      const logoutUrl = page.url();
      console.log("URL after logout:", logoutUrl);

      if (logoutUrl.includes("/admin/auth/signin")) {
        console.log("✓ Successfully logged out and redirected");
      } else {
        console.log("❌ Logout redirect failed");
      }
    } else {
      console.log(
        "? Logout button not found, checking alternative logout method"
      );
      // Try accessing admin again to see if session persists
      await page.goto("/admin");
      await page.waitForTimeout(2000);
      console.log("Direct admin access after login:", page.url());
    }

    console.log("=== Authentication Flow Test Complete ===");
  });
});
