#!/bin/bash

# Admin Dashboard Test Runner
# This script helps run different sets of admin dashboard tests

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if the development server is running
check_server() {
    print_status "Checking if development server is running..."
    if curl -s http://localhost:3000 > /dev/null; then
        print_success "Development server is running"
        return 0
    else
        print_error "Development server is not running"
        print_status "Please start the development server with: npm run dev"
        return 1
    fi
}

# Function to run tests with proper error handling
run_tests() {
    local test_pattern="$1"
    local description="$2"
    
    print_status "Running $description..."
    
    if npx playwright test "$test_pattern" --reporter=list; then
        print_success "$description completed successfully"
        return 0
    else
        print_error "$description failed"
        return 1
    fi
}

# Function to show usage
show_usage() {
    echo "Admin Dashboard Test Runner"
    echo ""
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  all              Run all implemented admin tests"
    echo "  auth             Run authentication tests"
    echo "  profile          Run profile management tests"
    echo "  dashboard        Run dashboard tests"
    echo "  analytics        Run analytics tests"
    echo "  products         Run products CRUD tests"
    echo "  categories       Run categories CRUD tests"
    echo "  settings         Run settings tests"
    echo "  workflow         Run workflow tests"
    echo "  fixes            Run bug fixes tests"
    echo "  placeholders     Run placeholder tests (will be skipped)"
    echo "  smoke            Run smoke tests"
    echo "  headed           Run all tests in headed mode"
    echo "  debug            Run all tests in debug mode"
    echo "  report           Generate and show test report"
    echo "  help             Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 all           # Run all implemented tests"
    echo "  $0 auth          # Run only authentication tests"
    echo "  $0 headed        # Run tests with browser UI visible"
    echo "  $0 debug         # Run tests in debug mode"
}

# Main script logic
main() {
    local command="${1:-help}"
    
    case "$command" in
        "all")
            check_server || exit 1
            print_status "Running all implemented admin dashboard tests..."
            run_tests "tests/e2e/admin-login.spec.ts tests/e2e/admin-profile.spec.ts tests/e2e/admin-dashboard.spec.ts tests/e2e/admin-analytics.spec.ts tests/e2e/admin-products.spec.ts tests/e2e/admin-categories.spec.ts tests/e2e/admin-settings.spec.ts" "All Admin Tests"
            ;;
        "auth")
            check_server || exit 1
            run_tests "tests/e2e/admin-login.spec.ts" "Authentication Tests"
            ;;
        "profile")
            check_server || exit 1
            run_tests "tests/e2e/admin-profile.spec.ts" "Profile Management Tests"
            ;;
        "dashboard")
            check_server || exit 1
            run_tests "tests/e2e/admin-dashboard.spec.ts" "Dashboard Tests"
            ;;
        "analytics")
            check_server || exit 1
            run_tests "tests/e2e/admin-analytics.spec.ts" "Analytics Tests"
            ;;
        "products")
            check_server || exit 1
            run_tests "tests/e2e/admin-products.spec.ts" "Products CRUD Tests"
            ;;
        "categories")
            check_server || exit 1
            run_tests "tests/e2e/admin-categories.spec.ts" "Categories CRUD Tests"
            ;;
        "settings")
            check_server || exit 1
            run_tests "tests/e2e/admin-settings.spec.ts" "Settings Tests"
            ;;
        "workflow")
            check_server || exit 1
            run_tests "tests/e2e/admin-workflow.spec.ts" "Workflow Tests"
            ;;
        "fixes")
            check_server || exit 1
            run_tests "tests/e2e/test-fixes.spec.ts" "Bug Fixes Tests"
            ;;
        "placeholders")
            check_server || exit 1
            print_warning "Running placeholder tests (these will be skipped until features are implemented)"
            run_tests "tests/e2e/admin-brands.spec.ts tests/e2e/admin-attributes.spec.ts tests/e2e/admin-inventory.spec.ts" "Placeholder Tests"
            ;;
        "smoke")
            check_server || exit 1
            run_tests "smoke" "Smoke Tests"
            ;;
        "headed")
            check_server || exit 1
            print_status "Running all tests in headed mode (browser UI visible)..."
            npx playwright test tests/e2e/admin-*.spec.ts --headed --reporter=list
            ;;
        "debug")
            check_server || exit 1
            print_status "Running tests in debug mode..."
            npx playwright test tests/e2e/admin-*.spec.ts --debug
            ;;
        "report")
            print_status "Generating and opening test report..."
            npx playwright show-report
            ;;
        "help"|"--help"|"-h")
            show_usage
            ;;
        *)
            print_error "Unknown command: $command"
            echo ""
            show_usage
            exit 1
            ;;
    esac
}

# Check if we're in the right directory
if [ ! -f "playwright.config.ts" ]; then
    print_error "playwright.config.ts not found. Please run this script from the project root directory."
    exit 1
fi

# Check if Playwright is installed
if ! command -v npx &> /dev/null; then
    print_error "npx not found. Please install Node.js and npm."
    exit 1
fi

# Run the main function
main "$@"
