import { test, expect } from "@playwright/test";

test.describe("Admin Auth Quick Test", () => {
  test("should access login page", async ({ page }) => {
    await page.goto("/admin/auth/signin");
    await expect(page.getByText("Admin Portal")).toBeVisible();
    await expect(page.locator("#email")).toBeVisible();
    await expect(page.locator("#password")).toBeVisible();
  });

  test("should redirect to login when accessing admin unauthenticated", async ({
    page,
  }) => {
    await page.goto("/admin");
    await expect(page).toHaveURL("/admin/auth/signin");
    await expect(page.getByText("Admin Portal")).toBeVisible();
  });

  test("should login and access dashboard", async ({ page }) => {
    // Go to login page
    await page.goto("/admin/auth/signin");

    // Fill credentials
    await page.locator("#email").fill("<EMAIL>");
    await page.locator("#password").fill("admin123");

    // Click login
    await page.click('button[type="submit"]');

    // Wait for navigation
    await page.waitForURL("/admin", { timeout: 10000 });

    // Should be on dashboard
    await expect(page).toHaveURL("/admin");
    await expect(page.getByText("Dashboard")).toBeVisible();
  });

  test("should logout properly", async ({ page }) => {
    // Login first
    await page.goto("/admin/auth/signin");
    await page.locator("#email").fill("<EMAIL>");
    await page.locator("#password").fill("admin123");
    await page.click('button[type="submit"]');
    await page.waitForURL("/admin", { timeout: 10000 });

    // Find and click logout button
    const logoutButton = page
      .locator('[data-testid="logout-button"]:visible')
      .first();
    await logoutButton.click();

    // Should be redirected to login
    await expect(page).toHaveURL("/admin/auth/signin");
    await expect(page.getByText("Admin Portal")).toBeVisible();
  });
});
