import { Page, Locator, expect } from "@playwright/test";

export class AdminCategoriesPage {
  readonly page: Page;
  readonly pageTitle: Locator;
  readonly createCategoryButton: Locator;
  readonly searchInput: Locator;
  readonly categoriesTable: Locator;
  readonly paginationContainer: Locator;
  
  // Category form elements
  readonly categoryNameInput: Locator;
  readonly categoryDescriptionInput: Locator;
  readonly categorySlugInput: Locator;
  readonly categoryParentSelect: Locator;
  readonly categoryImageInput: Locator;
  readonly saveCategoryButton: Locator;
  readonly cancelCategoryButton: Locator;
  
  // Action buttons
  readonly editButtons: Locator;
  readonly deleteButtons: Locator;
  readonly viewButtons: Locator;
  
  // Modal/Dialog elements
  readonly categoryModal: Locator;
  readonly deleteConfirmModal: Locator;
  readonly confirmDeleteButton: Locator;
  readonly cancelDeleteButton: Locator;
  
  // Messages
  readonly successMessage: Locator;
  readonly errorMessage: Locator;

  constructor(page: Page) {
    this.page = page;
    this.pageTitle = page.getByText("Quản lý danh mục");
    this.createCategoryButton = page.getByRole("button", { name: "Thêm danh mục" });
    this.searchInput = page.locator('input[placeholder*="Tìm kiếm"]');
    this.categoriesTable = page.locator('table, [data-testid="categories-table"]');
    this.paginationContainer = page.locator('[data-testid="pagination"], .pagination');
    
    // Category form
    this.categoryNameInput = page.locator('input[name="name"], #name');
    this.categoryDescriptionInput = page.locator('textarea[name="description"], #description');
    this.categorySlugInput = page.locator('input[name="slug"], #slug');
    this.categoryParentSelect = page.locator('select[name="parentId"], #parentId');
    this.categoryImageInput = page.locator('input[name="image"], #image');
    this.saveCategoryButton = page.getByRole("button", { name: "Lưu" });
    this.cancelCategoryButton = page.getByRole("button", { name: "Hủy" });
    
    // Action buttons
    this.editButtons = page.locator('[data-testid="edit-category"], button:has-text("Sửa")');
    this.deleteButtons = page.locator('[data-testid="delete-category"], button:has-text("Xóa")');
    this.viewButtons = page.locator('[data-testid="view-category"], button:has-text("Xem")');
    
    // Modals
    this.categoryModal = page.locator('[data-testid="category-modal"], .modal');
    this.deleteConfirmModal = page.locator('[data-testid="delete-confirm-modal"]');
    this.confirmDeleteButton = page.getByRole("button", { name: "Xác nhận xóa" });
    this.cancelDeleteButton = page.getByRole("button", { name: "Hủy bỏ" });
    
    // Messages
    this.successMessage = page.locator('[role="alert"]:has-text("thành công"), .success');
    this.errorMessage = page.locator('[role="alert"]:has-text("lỗi"), .error');
  }

  async goto() {
    await this.page.goto("/admin/categories");
    await expect(this.pageTitle).toBeVisible();
  }

  async createCategory(categoryData: {
    name: string;
    description?: string;
    slug: string;
    parentId?: string;
    image?: string;
  }) {
    await this.createCategoryButton.click();
    await expect(this.categoryModal).toBeVisible();
    
    await this.categoryNameInput.fill(categoryData.name);
    
    if (categoryData.description) {
      await this.categoryDescriptionInput.fill(categoryData.description);
    }
    
    await this.categorySlugInput.fill(categoryData.slug);
    
    if (categoryData.parentId) {
      await this.categoryParentSelect.selectOption(categoryData.parentId);
    }
    
    await this.saveCategoryButton.click();
  }

  async editCategory(categoryName: string, updates: Partial<{
    name: string;
    description: string;
    slug: string;
    parentId: string;
  }>) {
    await this.searchForCategory(categoryName);
    await this.editButtons.first().click();
    await expect(this.categoryModal).toBeVisible();
    
    if (updates.name) {
      await this.categoryNameInput.clear();
      await this.categoryNameInput.fill(updates.name);
    }
    
    if (updates.description) {
      await this.categoryDescriptionInput.clear();
      await this.categoryDescriptionInput.fill(updates.description);
    }
    
    if (updates.slug) {
      await this.categorySlugInput.clear();
      await this.categorySlugInput.fill(updates.slug);
    }
    
    if (updates.parentId) {
      await this.categoryParentSelect.selectOption(updates.parentId);
    }
    
    await this.saveCategoryButton.click();
  }

  async deleteCategory(categoryName: string) {
    await this.searchForCategory(categoryName);
    await this.deleteButtons.first().click();
    await expect(this.deleteConfirmModal).toBeVisible();
    await this.confirmDeleteButton.click();
  }

  async searchForCategory(categoryName: string) {
    await this.searchInput.fill(categoryName);
    await this.page.keyboard.press("Enter");
    await this.page.waitForTimeout(1000); // Wait for search results
  }

  async expectCategoryInList(categoryName: string) {
    await expect(this.categoriesTable).toContainText(categoryName);
  }

  async expectCategoryNotInList(categoryName: string) {
    await expect(this.categoriesTable).not.toContainText(categoryName);
  }

  async expectSuccessMessage(message?: string) {
    await expect(this.successMessage).toBeVisible();
    if (message) {
      await expect(this.successMessage).toContainText(message);
    }
  }

  async expectErrorMessage(message?: string) {
    await expect(this.errorMessage).toBeVisible();
    if (message) {
      await expect(this.errorMessage).toContainText(message);
    }
  }

  async expectCategoriesTableVisible() {
    await expect(this.categoriesTable).toBeVisible();
    await expect(this.createCategoryButton).toBeVisible();
    await expect(this.searchInput).toBeVisible();
  }

  async expectCategoryFormVisible() {
    await expect(this.categoryModal).toBeVisible();
    await expect(this.categoryNameInput).toBeVisible();
    await expect(this.categoryDescriptionInput).toBeVisible();
    await expect(this.categorySlugInput).toBeVisible();
    await expect(this.saveCategoryButton).toBeVisible();
  }

  async getCategoryCount(): Promise<number> {
    const rows = await this.categoriesTable.locator('tbody tr').count();
    return rows;
  }

  async navigateToPage(pageNumber: number) {
    await this.page.locator(`[data-testid="page-${pageNumber}"]`).click();
    await this.page.waitForTimeout(1000);
  }
}
