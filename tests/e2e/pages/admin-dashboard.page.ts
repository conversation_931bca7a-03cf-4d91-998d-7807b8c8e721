import { Page, Locator, expect } from "@playwright/test";

export class AdminDashboardPage {
  readonly page: Page;
  readonly userDropdownTrigger: Locator;
  readonly logoutButtonInDropdown: Locator;
  readonly logoutButtonInSidebar: Locator;
  readonly dashboardTitle: Locator;
  readonly sidebarCollapseButton: Locator;

  constructor(page: Page) {
    this.page = page;
    this.userDropdownTrigger = page.locator(
      'button:has(div:has-text("Admin"))'
    );
    this.logoutButtonInDropdown = page
      .locator('[data-testid="logout-button"]')
      .first();
    this.logoutButtonInSidebar = page.locator(
      'button[data-testid="logout-button"]:has-text("Đăng xuất")'
    );
    this.dashboardTitle = page.getByText("Dashboard");
    this.sidebarCollapseButton = page
      .locator('button[aria-label*="collapse"], button[aria-label*="toggle"]')
      .first();
  }

  async goto() {
    await this.page.goto("/admin");
    await expect(this.dashboardTitle).toBeVisible();
  }

  async openUserDropdown() {
    await this.userDropdownTrigger.click();
    await this.page.waitForTimeout(300);

    // Verify dropdown is open
    await expect(this.page.getByText("Tài khoản")).toBeVisible();
    await expect(this.page.getByText("Đăng xuất")).toBeVisible();
  }

  async closeUserDropdown() {
    await this.page.click("body");
    await this.page.waitForTimeout(300);
  }

  async logoutViaDropdown() {
    await this.openUserDropdown();
    await this.logoutButtonInDropdown.click();

    // Wait for redirect to login page
    await expect(this.page).toHaveURL("/admin/auth/signin");
    await expect(this.page.getByText("Admin Portal")).toBeVisible();
  }

  async logoutViaSidebar() {
    // Click the logout button in the sidebar
    await this.page.locator('[data-testid="logout-button"]').click();

    // Wait for redirect to login page with longer timeout
    await this.page.waitForURL("/admin/auth/signin", { timeout: 10000 });

    // Verify we're actually on the login page
    await expect(this.page.getByText("Admin Portal")).toBeVisible();
  }

  async expectDashboardVisible() {
    await expect(this.dashboardTitle).toBeVisible();
    await expect(
      this.page.getByText("Tổng quan về hoạt động kinh doanh")
    ).toBeVisible();
  }

  async expectLogoutButtonsVisible() {
    // Check dropdown logout button
    await this.openUserDropdown();
    await expect(this.logoutButtonInDropdown).toBeVisible();
    await expect(this.logoutButtonInDropdown).toContainText("Đăng xuất");
    await this.closeUserDropdown();

    // Check sidebar logout button if visible
    if (await this.logoutButtonInSidebar.isVisible()) {
      await expect(this.logoutButtonInSidebar).toContainText("Đăng xuất");
    }
  }

  async navigateToAdminPage(path: string) {
    await this.page.goto(`/admin${path}`);
    await this.page.waitForLoadState("networkidle");
  }

  async verifyCookieCleared() {
    const cookies = await this.page.context().cookies();
    const adminSessionCookie = cookies.find(
      (cookie) => cookie.name === "admin-session"
    );

    if (adminSessionCookie) {
      expect(adminSessionCookie.value).toBe("");
    }
  }

  async verifyAdminAccessDenied() {
    const adminPages = [
      "/admin",
      "/admin/products",
      "/admin/users",
      "/admin/orders",
      "/admin/settings",
    ];

    for (const adminPage of adminPages) {
      await this.page.goto(adminPage);

      // Should always redirect to login
      await expect(this.page).toHaveURL("/admin/auth/signin");
      await expect(this.page.getByText("Admin Portal")).toBeVisible();
    }
  }
}
