import { Page, Locator, expect } from "@playwright/test";

/**
 * Page Object for Admin Brands Management
 * NOTE: This is a placeholder for future implementation.
 * The brands feature is not yet implemented in the application.
 */
export class AdminBrandsPage {
  readonly page: Page;
  readonly pageTitle: Locator;
  readonly createBrandButton: Locator;
  readonly searchInput: Locator;
  readonly brandsTable: Locator;
  readonly paginationContainer: Locator;
  
  // Brand form elements
  readonly brandNameInput: Locator;
  readonly brandDescriptionInput: Locator;
  readonly brandSlugInput: Locator;
  readonly brandLogoInput: Locator;
  readonly brandWebsiteInput: Locator;
  readonly saveBrandButton: Locator;
  readonly cancelBrandButton: Locator;
  
  // Action buttons
  readonly editButtons: Locator;
  readonly deleteButtons: Locator;
  readonly viewButtons: Locator;
  
  // Modal/Dialog elements
  readonly brandModal: Locator;
  readonly deleteConfirmModal: Locator;
  readonly confirmDeleteButton: Locator;
  readonly cancelDeleteButton: Locator;
  
  // Messages
  readonly successMessage: Locator;
  readonly errorMessage: Locator;

  constructor(page: Page) {
    this.page = page;
    this.pageTitle = page.getByText("Quản lý thương hiệu");
    this.createBrandButton = page.getByRole("button", { name: "Thêm thương hiệu" });
    this.searchInput = page.locator('input[placeholder*="Tìm kiếm"]');
    this.brandsTable = page.locator('table, [data-testid="brands-table"]');
    this.paginationContainer = page.locator('[data-testid="pagination"], .pagination');
    
    // Brand form
    this.brandNameInput = page.locator('input[name="name"], #name');
    this.brandDescriptionInput = page.locator('textarea[name="description"], #description');
    this.brandSlugInput = page.locator('input[name="slug"], #slug');
    this.brandLogoInput = page.locator('input[name="logo"], #logo');
    this.brandWebsiteInput = page.locator('input[name="website"], #website');
    this.saveBrandButton = page.getByRole("button", { name: "Lưu" });
    this.cancelBrandButton = page.getByRole("button", { name: "Hủy" });
    
    // Action buttons
    this.editButtons = page.locator('[data-testid="edit-brand"], button:has-text("Sửa")');
    this.deleteButtons = page.locator('[data-testid="delete-brand"], button:has-text("Xóa")');
    this.viewButtons = page.locator('[data-testid="view-brand"], button:has-text("Xem")');
    
    // Modals
    this.brandModal = page.locator('[data-testid="brand-modal"], .modal');
    this.deleteConfirmModal = page.locator('[data-testid="delete-confirm-modal"]');
    this.confirmDeleteButton = page.getByRole("button", { name: "Xác nhận xóa" });
    this.cancelDeleteButton = page.getByRole("button", { name: "Hủy bỏ" });
    
    // Messages
    this.successMessage = page.locator('[role="alert"]:has-text("thành công"), .success');
    this.errorMessage = page.locator('[role="alert"]:has-text("lỗi"), .error');
  }

  async goto() {
    await this.page.goto("/admin/brands");
    // NOTE: This will likely fail until the brands feature is implemented
    await expect(this.pageTitle).toBeVisible();
  }

  async createBrand(brandData: {
    name: string;
    description?: string;
    slug: string;
    logo?: string;
    website?: string;
  }) {
    await this.createBrandButton.click();
    await expect(this.brandModal).toBeVisible();
    
    await this.brandNameInput.fill(brandData.name);
    
    if (brandData.description) {
      await this.brandDescriptionInput.fill(brandData.description);
    }
    
    await this.brandSlugInput.fill(brandData.slug);
    
    if (brandData.logo) {
      await this.brandLogoInput.fill(brandData.logo);
    }
    
    if (brandData.website) {
      await this.brandWebsiteInput.fill(brandData.website);
    }
    
    await this.saveBrandButton.click();
  }

  async editBrand(brandName: string, updates: Partial<{
    name: string;
    description: string;
    slug: string;
    website: string;
  }>) {
    await this.searchForBrand(brandName);
    await this.editButtons.first().click();
    await expect(this.brandModal).toBeVisible();
    
    if (updates.name) {
      await this.brandNameInput.clear();
      await this.brandNameInput.fill(updates.name);
    }
    
    if (updates.description) {
      await this.brandDescriptionInput.clear();
      await this.brandDescriptionInput.fill(updates.description);
    }
    
    if (updates.slug) {
      await this.brandSlugInput.clear();
      await this.brandSlugInput.fill(updates.slug);
    }
    
    if (updates.website) {
      await this.brandWebsiteInput.clear();
      await this.brandWebsiteInput.fill(updates.website);
    }
    
    await this.saveBrandButton.click();
  }

  async deleteBrand(brandName: string) {
    await this.searchForBrand(brandName);
    await this.deleteButtons.first().click();
    await expect(this.deleteConfirmModal).toBeVisible();
    await this.confirmDeleteButton.click();
  }

  async searchForBrand(brandName: string) {
    await this.searchInput.fill(brandName);
    await this.page.keyboard.press("Enter");
    await this.page.waitForTimeout(1000);
  }

  async expectBrandInList(brandName: string) {
    await expect(this.brandsTable).toContainText(brandName);
  }

  async expectBrandNotInList(brandName: string) {
    await expect(this.brandsTable).not.toContainText(brandName);
  }

  async expectSuccessMessage(message?: string) {
    await expect(this.successMessage).toBeVisible();
    if (message) {
      await expect(this.successMessage).toContainText(message);
    }
  }

  async expectErrorMessage(message?: string) {
    await expect(this.errorMessage).toBeVisible();
    if (message) {
      await expect(this.errorMessage).toContainText(message);
    }
  }

  async expectBrandsTableVisible() {
    await expect(this.brandsTable).toBeVisible();
    await expect(this.createBrandButton).toBeVisible();
    await expect(this.searchInput).toBeVisible();
  }

  async expectBrandFormVisible() {
    await expect(this.brandModal).toBeVisible();
    await expect(this.brandNameInput).toBeVisible();
    await expect(this.brandDescriptionInput).toBeVisible();
    await expect(this.brandSlugInput).toBeVisible();
    await expect(this.saveBrandButton).toBeVisible();
  }

  async getBrandCount(): Promise<number> {
    const rows = await this.brandsTable.locator('tbody tr').count();
    return rows;
  }
}
