import { Page, Locator, expect } from "@playwright/test";

export class AdminProductsPage {
  readonly page: Page;
  readonly pageTitle: Locator;
  readonly createProductButton: Locator;
  readonly searchInput: Locator;
  readonly filterSelect: Locator;
  readonly productsTable: Locator;
  readonly paginationContainer: Locator;
  
  // Product form elements
  readonly productNameInput: Locator;
  readonly productDescriptionInput: Locator;
  readonly productPriceInput: Locator;
  readonly productSalePriceInput: Locator;
  readonly productSkuInput: Locator;
  readonly productStockInput: Locator;
  readonly productCategorySelect: Locator;
  readonly productImagesInput: Locator;
  readonly productFeaturedCheckbox: Locator;
  readonly productStatusSelect: Locator;
  readonly productTagsInput: Locator;
  readonly saveProductButton: Locator;
  readonly cancelProductButton: Locator;
  
  // Action buttons
  readonly editButtons: Locator;
  readonly deleteButtons: Locator;
  readonly viewButtons: Locator;
  
  // Modal/Dialog elements
  readonly productModal: Locator;
  readonly deleteConfirmModal: Locator;
  readonly confirmDeleteButton: Locator;
  readonly cancelDeleteButton: Locator;
  
  // Messages
  readonly successMessage: Locator;
  readonly errorMessage: Locator;

  constructor(page: Page) {
    this.page = page;
    this.pageTitle = page.getByText("Quản lý sản phẩm");
    this.createProductButton = page.getByRole("button", { name: "Thêm sản phẩm" });
    this.searchInput = page.locator('input[placeholder*="Tìm kiếm"]');
    this.filterSelect = page.locator('select[name="filter"], [data-testid="filter-select"]');
    this.productsTable = page.locator('table, [data-testid="products-table"]');
    this.paginationContainer = page.locator('[data-testid="pagination"], .pagination');
    
    // Product form
    this.productNameInput = page.locator('input[name="name"], #name');
    this.productDescriptionInput = page.locator('textarea[name="description"], #description');
    this.productPriceInput = page.locator('input[name="price"], #price');
    this.productSalePriceInput = page.locator('input[name="salePrice"], #salePrice');
    this.productSkuInput = page.locator('input[name="sku"], #sku');
    this.productStockInput = page.locator('input[name="stock"], #stock');
    this.productCategorySelect = page.locator('select[name="categoryId"], #categoryId');
    this.productImagesInput = page.locator('input[name="images"], #images');
    this.productFeaturedCheckbox = page.locator('input[name="featured"], #featured');
    this.productStatusSelect = page.locator('select[name="status"], #status');
    this.productTagsInput = page.locator('input[name="tags"], #tags');
    this.saveProductButton = page.getByRole("button", { name: "Lưu" });
    this.cancelProductButton = page.getByRole("button", { name: "Hủy" });
    
    // Action buttons
    this.editButtons = page.locator('[data-testid="edit-product"], button:has-text("Sửa")');
    this.deleteButtons = page.locator('[data-testid="delete-product"], button:has-text("Xóa")');
    this.viewButtons = page.locator('[data-testid="view-product"], button:has-text("Xem")');
    
    // Modals
    this.productModal = page.locator('[data-testid="product-modal"], .modal');
    this.deleteConfirmModal = page.locator('[data-testid="delete-confirm-modal"]');
    this.confirmDeleteButton = page.getByRole("button", { name: "Xác nhận xóa" });
    this.cancelDeleteButton = page.getByRole("button", { name: "Hủy bỏ" });
    
    // Messages
    this.successMessage = page.locator('[role="alert"]:has-text("thành công"), .success');
    this.errorMessage = page.locator('[role="alert"]:has-text("lỗi"), .error');
  }

  async goto() {
    await this.page.goto("/admin/products");
    await expect(this.pageTitle).toBeVisible();
  }

  async createProduct(productData: {
    name: string;
    description: string;
    price: number;
    salePrice?: number;
    sku: string;
    stock: number;
    categoryId: string;
    images?: string[];
    featured?: boolean;
    status?: string;
    tags?: string[];
  }) {
    await this.createProductButton.click();
    await expect(this.productModal).toBeVisible();
    
    await this.productNameInput.fill(productData.name);
    await this.productDescriptionInput.fill(productData.description);
    await this.productPriceInput.fill(productData.price.toString());
    
    if (productData.salePrice) {
      await this.productSalePriceInput.fill(productData.salePrice.toString());
    }
    
    await this.productSkuInput.fill(productData.sku);
    await this.productStockInput.fill(productData.stock.toString());
    await this.productCategorySelect.selectOption(productData.categoryId);
    
    if (productData.featured) {
      await this.productFeaturedCheckbox.check();
    }
    
    if (productData.status) {
      await this.productStatusSelect.selectOption(productData.status);
    }
    
    await this.saveProductButton.click();
  }

  async editProduct(productName: string, updates: Partial<{
    name: string;
    description: string;
    price: number;
    stock: number;
  }>) {
    await this.searchForProduct(productName);
    await this.editButtons.first().click();
    await expect(this.productModal).toBeVisible();
    
    if (updates.name) {
      await this.productNameInput.clear();
      await this.productNameInput.fill(updates.name);
    }
    
    if (updates.description) {
      await this.productDescriptionInput.clear();
      await this.productDescriptionInput.fill(updates.description);
    }
    
    if (updates.price) {
      await this.productPriceInput.clear();
      await this.productPriceInput.fill(updates.price.toString());
    }
    
    if (updates.stock !== undefined) {
      await this.productStockInput.clear();
      await this.productStockInput.fill(updates.stock.toString());
    }
    
    await this.saveProductButton.click();
  }

  async deleteProduct(productName: string) {
    await this.searchForProduct(productName);
    await this.deleteButtons.first().click();
    await expect(this.deleteConfirmModal).toBeVisible();
    await this.confirmDeleteButton.click();
  }

  async searchForProduct(productName: string) {
    await this.searchInput.fill(productName);
    await this.page.keyboard.press("Enter");
    await this.page.waitForTimeout(1000); // Wait for search results
  }

  async expectProductInList(productName: string) {
    await expect(this.productsTable).toContainText(productName);
  }

  async expectProductNotInList(productName: string) {
    await expect(this.productsTable).not.toContainText(productName);
  }

  async expectSuccessMessage(message?: string) {
    await expect(this.successMessage).toBeVisible();
    if (message) {
      await expect(this.successMessage).toContainText(message);
    }
  }

  async expectErrorMessage(message?: string) {
    await expect(this.errorMessage).toBeVisible();
    if (message) {
      await expect(this.errorMessage).toContainText(message);
    }
  }

  async expectProductsTableVisible() {
    await expect(this.productsTable).toBeVisible();
    await expect(this.createProductButton).toBeVisible();
    await expect(this.searchInput).toBeVisible();
  }

  async expectProductFormVisible() {
    await expect(this.productModal).toBeVisible();
    await expect(this.productNameInput).toBeVisible();
    await expect(this.productDescriptionInput).toBeVisible();
    await expect(this.productPriceInput).toBeVisible();
    await expect(this.saveProductButton).toBeVisible();
  }

  async getProductCount(): Promise<number> {
    const rows = await this.productsTable.locator('tbody tr').count();
    return rows;
  }

  async navigateToPage(pageNumber: number) {
    await this.page.locator(`[data-testid="page-${pageNumber}"]`).click();
    await this.page.waitForTimeout(1000);
  }
}
