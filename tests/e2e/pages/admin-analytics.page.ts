import { Page, Locator, expect } from "@playwright/test";

export class AdminAnalyticsPage {
  readonly page: Page;
  readonly pageTitle: Locator;
  readonly dateRangePicker: Locator;
  readonly filterSelect: Locator;
  readonly revenueChart: Locator;
  readonly ordersChart: Locator;
  readonly topProductsTable: Locator;
  readonly topCustomersTable: Locator;
  readonly exportButton: Locator;
  readonly printButton: Locator;
  readonly refreshButton: Locator;
  
  // Filter elements
  readonly startDateInput: Locator;
  readonly endDateInput: Locator;
  readonly applyFilterButton: Locator;
  readonly resetFilterButton: Locator;
  
  // Summary cards
  readonly totalRevenueCard: Locator;
  readonly totalOrdersCard: Locator;
  readonly totalCustomersCard: Locator;
  readonly averageOrderValueCard: Locator;
  
  // Messages
  readonly loadingIndicator: Locator;
  readonly errorMessage: Locator;

  constructor(page: Page) {
    this.page = page;
    this.pageTitle = page.getByText("Thống kê & Báo cáo");
    this.dateRangePicker = page.locator('[data-testid="date-range-picker"]');
    this.filterSelect = page.locator('select[name="filter"], [data-testid="filter-select"]');
    this.revenueChart = page.locator('[data-testid="revenue-chart"]');
    this.ordersChart = page.locator('[data-testid="orders-chart"]');
    this.topProductsTable = page.locator('[data-testid="top-products-table"]');
    this.topCustomersTable = page.locator('[data-testid="top-customers-table"]');
    this.exportButton = page.getByRole("button", { name: "Xuất báo cáo" });
    this.printButton = page.getByRole("button", { name: "In báo cáo" });
    this.refreshButton = page.getByRole("button", { name: "Làm mới" });
    
    // Filter elements
    this.startDateInput = page.locator('input[name="startDate"]');
    this.endDateInput = page.locator('input[name="endDate"]');
    this.applyFilterButton = page.getByRole("button", { name: "Áp dụng" });
    this.resetFilterButton = page.getByRole("button", { name: "Đặt lại" });
    
    // Summary cards
    this.totalRevenueCard = page.locator('[data-testid="total-revenue-card"]');
    this.totalOrdersCard = page.locator('[data-testid="total-orders-card"]');
    this.totalCustomersCard = page.locator('[data-testid="total-customers-card"]');
    this.averageOrderValueCard = page.locator('[data-testid="avg-order-value-card"]');
    
    // Messages
    this.loadingIndicator = page.locator('[data-testid="loading-indicator"]');
    this.errorMessage = page.locator('[role="alert"]:has-text("lỗi"), .error');
  }

  async goto() {
    await this.page.goto("/admin/analytics");
    await expect(this.pageTitle).toBeVisible();
    await this.waitForChartsToLoad();
  }

  async waitForChartsToLoad() {
    // Wait for loading indicator to disappear
    if (await this.loadingIndicator.isVisible()) {
      await expect(this.loadingIndicator).not.toBeVisible({ timeout: 10000 });
    }
    
    // Wait for charts to be visible
    await expect(this.revenueChart).toBeVisible();
    await expect(this.ordersChart).toBeVisible();
  }

  async filterByDateRange(startDate: string, endDate: string) {
    await this.dateRangePicker.click();
    await this.startDateInput.fill(startDate);
    await this.endDateInput.fill(endDate);
    await this.applyFilterButton.click();
    await this.waitForChartsToLoad();
  }

  async filterByPredefinedRange(range: 'today' | 'yesterday' | 'last7days' | 'last30days' | 'thisMonth' | 'lastMonth') {
    await this.filterSelect.selectOption(range);
    await this.waitForChartsToLoad();
  }

  async exportReport(format: 'pdf' | 'excel' | 'csv') {
    // This is a placeholder - actual implementation would depend on how export is implemented
    await this.exportButton.click();
    // Select format from dropdown or similar
    await this.page.getByText(format, { exact: false }).click();
  }

  async printReport() {
    // This is a placeholder - actual implementation would depend on how print is implemented
    await this.printButton.click();
  }

  async refreshData() {
    await this.refreshButton.click();
    await this.waitForChartsToLoad();
  }

  async expectAnalyticsPageVisible() {
    await expect(this.pageTitle).toBeVisible();
    await expect(this.revenueChart).toBeVisible();
    await expect(this.ordersChart).toBeVisible();
    await expect(this.topProductsTable).toBeVisible();
  }

  async expectSummaryCardsVisible() {
    await expect(this.totalRevenueCard).toBeVisible();
    await expect(this.totalOrdersCard).toBeVisible();
    await expect(this.totalCustomersCard).toBeVisible();
    await expect(this.averageOrderValueCard).toBeVisible();
  }

  async expectTopProductsTableVisible() {
    await expect(this.topProductsTable).toBeVisible();
    // Check if table has content
    const rowCount = await this.topProductsTable.locator('tbody tr').count();
    expect(rowCount).toBeGreaterThan(0);
  }

  async expectTopCustomersTableVisible() {
    await expect(this.topCustomersTable).toBeVisible();
    // Check if table has content
    const rowCount = await this.topCustomersTable.locator('tbody tr').count();
    expect(rowCount).toBeGreaterThan(0);
  }

  async expectErrorMessage(message?: string) {
    await expect(this.errorMessage).toBeVisible();
    if (message) {
      await expect(this.errorMessage).toContainText(message);
    }
  }

  async expectDataInDateRange(startDate: string, endDate: string) {
    // This is a placeholder - actual implementation would depend on how date range is displayed
    await expect(this.page).toContainText(startDate);
    await expect(this.page).toContainText(endDate);
  }
}
