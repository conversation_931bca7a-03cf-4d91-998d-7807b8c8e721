import { Page, Locator, expect } from "@playwright/test";

export class AdminProfilePage {
  readonly page: Page;
  readonly profileTitle: Locator;
  readonly nameInput: Locator;
  readonly emailInput: Locator;
  readonly phoneInput: Locator;
  readonly saveProfileButton: Locator;
  readonly cancelProfileButton: Locator;
  
  // Password change section
  readonly currentPasswordInput: Locator;
  readonly newPasswordInput: Locator;
  readonly confirmPasswordInput: Locator;
  readonly changePasswordButton: Locator;
  readonly cancelPasswordButton: Locator;
  
  // Security settings
  readonly twoFactorToggle: Locator;
  readonly activateTwoFactorButton: Locator;
  
  // Success/error messages
  readonly successMessage: Locator;
  readonly errorMessage: Locator;

  constructor(page: Page) {
    this.page = page;
    this.profileTitle = page.getByText("Thông tin cá nhân");
    
    // Profile form inputs
    this.nameInput = page.locator('input[type="text"]').first();
    this.emailInput = page.locator('input[type="email"]');
    this.phoneInput = page.locator('input[type="tel"]');
    this.saveProfileButton = page.getByRole("button", { name: "Lưu thay đổi" });
    this.cancelProfileButton = page.getByRole("button", { name: "Hủy" });
    
    // Password change inputs
    this.currentPasswordInput = page.locator('input[type="password"]').first();
    this.newPasswordInput = page.locator('input[type="password"]').nth(1);
    this.confirmPasswordInput = page.locator('input[type="password"]').nth(2);
    this.changePasswordButton = page.getByRole("button", { name: "Đổi mật khẩu" });
    this.cancelPasswordButton = page.getByRole("button", { name: "Hủy" }).nth(1);
    
    // Security settings
    this.twoFactorToggle = page.locator('[data-testid="2fa-toggle"]');
    this.activateTwoFactorButton = page.getByRole("button", { name: "Kích hoạt" });
    
    // Messages
    this.successMessage = page.locator('[role="alert"]:has-text("thành công"), .success, [class*="success"]');
    this.errorMessage = page.locator('[role="alert"]:has-text("lỗi"), .error, [class*="error"]');
  }

  async goto() {
    await this.page.goto("/admin/profile");
    await expect(this.profileTitle).toBeVisible();
  }

  async updateProfile(data: { name?: string; email?: string; phone?: string }) {
    if (data.name) {
      await this.nameInput.clear();
      await this.nameInput.fill(data.name);
    }
    
    if (data.email) {
      await this.emailInput.clear();
      await this.emailInput.fill(data.email);
    }
    
    if (data.phone) {
      await this.phoneInput.clear();
      await this.phoneInput.fill(data.phone);
    }
    
    await this.saveProfileButton.click();
  }

  async changePassword(currentPassword: string, newPassword: string, confirmPassword?: string) {
    await this.currentPasswordInput.fill(currentPassword);
    await this.newPasswordInput.fill(newPassword);
    await this.confirmPasswordInput.fill(confirmPassword || newPassword);
    await this.changePasswordButton.click();
  }

  async activateTwoFactor() {
    await this.activateTwoFactorButton.click();
  }

  async expectProfileFormVisible() {
    await expect(this.profileTitle).toBeVisible();
    await expect(this.nameInput).toBeVisible();
    await expect(this.emailInput).toBeVisible();
    await expect(this.phoneInput).toBeVisible();
    await expect(this.saveProfileButton).toBeVisible();
  }

  async expectPasswordFormVisible() {
    await expect(this.currentPasswordInput).toBeVisible();
    await expect(this.newPasswordInput).toBeVisible();
    await expect(this.confirmPasswordInput).toBeVisible();
    await expect(this.changePasswordButton).toBeVisible();
  }

  async expectSuccessMessage(message?: string) {
    await expect(this.successMessage).toBeVisible();
    if (message) {
      await expect(this.successMessage).toContainText(message);
    }
  }

  async expectErrorMessage(message?: string) {
    await expect(this.errorMessage).toBeVisible();
    if (message) {
      await expect(this.errorMessage).toContainText(message);
    }
  }

  async expectProfileData(data: { name?: string; email?: string; phone?: string }) {
    if (data.name) {
      await expect(this.nameInput).toHaveValue(data.name);
    }
    
    if (data.email) {
      await expect(this.emailInput).toHaveValue(data.email);
    }
    
    if (data.phone) {
      await expect(this.phoneInput).toHaveValue(data.phone);
    }
  }

  async expectSecuritySettingsVisible() {
    await expect(this.activateTwoFactorButton).toBeVisible();
    await expect(this.page.getByText("Xác thực hai yếu tố")).toBeVisible();
  }
}
