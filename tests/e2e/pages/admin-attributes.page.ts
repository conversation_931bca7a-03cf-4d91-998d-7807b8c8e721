import { Page, Locator, expect } from "@playwright/test";

/**
 * Page Object for Admin Attributes Management
 * NOTE: This is a placeholder for future implementation.
 * The attributes feature is not yet implemented in the application.
 */
export class AdminAttributesPage {
  readonly page: Page;
  readonly pageTitle: Locator;
  readonly createAttributeButton: Locator;
  readonly searchInput: Locator;
  readonly attributesTable: Locator;
  readonly paginationContainer: Locator;
  
  // Attribute form elements
  readonly attributeNameInput: Locator;
  readonly attributeTypeSelect: Locator;
  readonly attributeRequiredCheckbox: Locator;
  readonly attributeOptionsInput: Locator;
  readonly attributeDescriptionInput: Locator;
  readonly saveAttributeButton: Locator;
  readonly cancelAttributeButton: Locator;
  
  // Action buttons
  readonly editButtons: Locator;
  readonly deleteButtons: Locator;
  readonly viewButtons: Locator;
  
  // Modal/Dialog elements
  readonly attributeModal: Locator;
  readonly deleteConfirmModal: Locator;
  readonly confirmDeleteButton: Locator;
  readonly cancelDeleteButton: Locator;
  
  // Messages
  readonly successMessage: Locator;
  readonly errorMessage: Locator;

  constructor(page: Page) {
    this.page = page;
    this.pageTitle = page.getByText("Quản lý thuộc tính");
    this.createAttributeButton = page.getByRole("button", { name: "Thêm thuộc tính" });
    this.searchInput = page.locator('input[placeholder*="Tìm kiếm"]');
    this.attributesTable = page.locator('table, [data-testid="attributes-table"]');
    this.paginationContainer = page.locator('[data-testid="pagination"], .pagination');
    
    // Attribute form
    this.attributeNameInput = page.locator('input[name="name"], #name');
    this.attributeTypeSelect = page.locator('select[name="type"], #type');
    this.attributeRequiredCheckbox = page.locator('input[name="required"], #required');
    this.attributeOptionsInput = page.locator('textarea[name="options"], #options');
    this.attributeDescriptionInput = page.locator('textarea[name="description"], #description');
    this.saveAttributeButton = page.getByRole("button", { name: "Lưu" });
    this.cancelAttributeButton = page.getByRole("button", { name: "Hủy" });
    
    // Action buttons
    this.editButtons = page.locator('[data-testid="edit-attribute"], button:has-text("Sửa")');
    this.deleteButtons = page.locator('[data-testid="delete-attribute"], button:has-text("Xóa")');
    this.viewButtons = page.locator('[data-testid="view-attribute"], button:has-text("Xem")');
    
    // Modals
    this.attributeModal = page.locator('[data-testid="attribute-modal"], .modal');
    this.deleteConfirmModal = page.locator('[data-testid="delete-confirm-modal"]');
    this.confirmDeleteButton = page.getByRole("button", { name: "Xác nhận xóa" });
    this.cancelDeleteButton = page.getByRole("button", { name: "Hủy bỏ" });
    
    // Messages
    this.successMessage = page.locator('[role="alert"]:has-text("thành công"), .success');
    this.errorMessage = page.locator('[role="alert"]:has-text("lỗi"), .error');
  }

  async goto() {
    await this.page.goto("/admin/attributes");
    // NOTE: This will likely fail until the attributes feature is implemented
    await expect(this.pageTitle).toBeVisible();
  }

  async createAttribute(attributeData: {
    name: string;
    type: 'text' | 'number' | 'select' | 'multiselect' | 'boolean';
    required?: boolean;
    options?: string[];
    description?: string;
  }) {
    await this.createAttributeButton.click();
    await expect(this.attributeModal).toBeVisible();
    
    await this.attributeNameInput.fill(attributeData.name);
    await this.attributeTypeSelect.selectOption(attributeData.type);
    
    if (attributeData.required) {
      await this.attributeRequiredCheckbox.check();
    }
    
    if (attributeData.options && attributeData.options.length > 0) {
      await this.attributeOptionsInput.fill(attributeData.options.join('\n'));
    }
    
    if (attributeData.description) {
      await this.attributeDescriptionInput.fill(attributeData.description);
    }
    
    await this.saveAttributeButton.click();
  }

  async editAttribute(attributeName: string, updates: Partial<{
    name: string;
    type: string;
    required: boolean;
    options: string[];
    description: string;
  }>) {
    await this.searchForAttribute(attributeName);
    await this.editButtons.first().click();
    await expect(this.attributeModal).toBeVisible();
    
    if (updates.name) {
      await this.attributeNameInput.clear();
      await this.attributeNameInput.fill(updates.name);
    }
    
    if (updates.type) {
      await this.attributeTypeSelect.selectOption(updates.type);
    }
    
    if (updates.required !== undefined) {
      if (updates.required) {
        await this.attributeRequiredCheckbox.check();
      } else {
        await this.attributeRequiredCheckbox.uncheck();
      }
    }
    
    if (updates.options) {
      await this.attributeOptionsInput.clear();
      await this.attributeOptionsInput.fill(updates.options.join('\n'));
    }
    
    if (updates.description) {
      await this.attributeDescriptionInput.clear();
      await this.attributeDescriptionInput.fill(updates.description);
    }
    
    await this.saveAttributeButton.click();
  }

  async deleteAttribute(attributeName: string) {
    await this.searchForAttribute(attributeName);
    await this.deleteButtons.first().click();
    await expect(this.deleteConfirmModal).toBeVisible();
    await this.confirmDeleteButton.click();
  }

  async searchForAttribute(attributeName: string) {
    await this.searchInput.fill(attributeName);
    await this.page.keyboard.press("Enter");
    await this.page.waitForTimeout(1000);
  }

  async expectAttributeInList(attributeName: string) {
    await expect(this.attributesTable).toContainText(attributeName);
  }

  async expectAttributeNotInList(attributeName: string) {
    await expect(this.attributesTable).not.toContainText(attributeName);
  }

  async expectSuccessMessage(message?: string) {
    await expect(this.successMessage).toBeVisible();
    if (message) {
      await expect(this.successMessage).toContainText(message);
    }
  }

  async expectErrorMessage(message?: string) {
    await expect(this.errorMessage).toBeVisible();
    if (message) {
      await expect(this.errorMessage).toContainText(message);
    }
  }

  async expectAttributesTableVisible() {
    await expect(this.attributesTable).toBeVisible();
    await expect(this.createAttributeButton).toBeVisible();
    await expect(this.searchInput).toBeVisible();
  }

  async expectAttributeFormVisible() {
    await expect(this.attributeModal).toBeVisible();
    await expect(this.attributeNameInput).toBeVisible();
    await expect(this.attributeTypeSelect).toBeVisible();
    await expect(this.saveAttributeButton).toBeVisible();
  }

  async getAttributeCount(): Promise<number> {
    const rows = await this.attributesTable.locator('tbody tr').count();
    return rows;
  }
}
