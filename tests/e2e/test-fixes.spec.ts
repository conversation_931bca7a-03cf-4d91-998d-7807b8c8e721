import { expect, test } from "@playwright/test";
import { AdminLoginPage } from "./pages/admin-login.page";
import { AdminDashboardPage } from "./pages/admin-dashboard.page";
import { AdminAnalyticsPage } from "./pages/admin-analytics.page";

test.describe("Admin Dashboard Fixes", () => {
  let loginPage: AdminLoginPage;
  let dashboardPage: AdminDashboardPage;
  let analyticsPage: AdminAnalyticsPage;

  test.beforeEach(async ({ page }) => {
    loginPage = new AdminLoginPage(page);
    dashboardPage = new AdminDashboardPage(page);
    analyticsPage = new AdminAnalyticsPage(page);
  });

  test.describe("Logout Functionality", () => {
    test("should logout successfully via sidebar", async ({ page }) => {
      // Login first
      await loginPage.loginAsAdmin();

      // Go to dashboard
      await dashboardPage.goto();
      await dashboardPage.expectDashboardVisible();

      // Logout via sidebar
      await dashboardPage.logoutViaSidebar();

      // Should be redirected to login page
      await expect(page).toHaveURL("/admin/auth/signin");
      await expect(page.getByText("Admin Portal")).toBeVisible();
    });

    test("should clear all session data on logout", async ({ page }) => {
      // Login first
      await loginPage.loginAsAdmin();

      // Check that admin session cookie exists
      const cookies = await page.context().cookies();
      const adminSessionCookie = cookies.find(
        (cookie) => cookie.name === "admin-session"
      );
      expect(adminSessionCookie).toBeTruthy();

      // Logout
      await dashboardPage.goto();
      await dashboardPage.logoutViaSidebar();

      // Check that session cookies are cleared
      const cookiesAfterLogout = await page.context().cookies();
      const adminSessionAfterLogout = cookiesAfterLogout.find(
        (cookie) => cookie.name === "admin-session"
      );
      expect(adminSessionAfterLogout).toBeFalsy();
    });

    test("should prevent access to admin pages after logout", async ({
      page,
    }) => {
      // Login first
      await loginPage.loginAsAdmin();

      // Logout
      await dashboardPage.goto();
      await dashboardPage.logoutViaSidebar();

      // Try to access admin dashboard directly
      await page.goto("/admin");

      // Should be redirected to login
      await expect(page).toHaveURL("/admin/auth/signin");
    });
  });

  test.describe("Analytics Page", () => {
    test("should load analytics page without errors", async () => {
      // Login as admin
      await loginPage.loginAsAdmin();

      // Go to analytics page
      await analyticsPage.goto();

      // Should display analytics page correctly
      await analyticsPage.expectAnalyticsPageVisible();
    });

    test("should display summary cards", async () => {
      // Login as admin
      await loginPage.loginAsAdmin();

      // Go to analytics page
      await analyticsPage.goto();

      // Should display summary cards
      await analyticsPage.expectSummaryCardsVisible();
    });

    test("should allow moderator to access analytics", async () => {
      // Login as moderator
      await loginPage.loginAsModerator();

      // Go to analytics page
      await analyticsPage.goto();

      // Should display analytics page correctly
      await analyticsPage.expectAnalyticsPageVisible();
    });

    test("should handle API errors gracefully", async ({ page }) => {
      // Login as admin
      await loginPage.loginAsAdmin();

      // Intercept analytics API and return error
      await page.route("/api/admin/analytics*", (route) => {
        route.fulfill({
          status: 500,
          contentType: "application/json",
          body: JSON.stringify({ error: "Server error" }),
        });
      });

      // Go to analytics page
      await page.goto("/admin/analytics");

      // Should handle error gracefully (not crash)
      await expect(page.getByText("Thống kê & Báo cáo")).toBeVisible();
      await expect(page.getByText("Có lỗi xảy ra")).toBeVisible();
      await expect(page.getByText("Server error")).toBeVisible();
    });

    test("should show retry button on error", async ({ page }) => {
      // Login as admin
      await loginPage.loginAsAdmin();

      // Intercept analytics API and return error first time
      let callCount = 0;
      await page.route("/api/admin/analytics*", (route) => {
        callCount++;
        if (callCount === 1) {
          route.fulfill({
            status: 500,
            contentType: "application/json",
            body: JSON.stringify({ error: "Server error" }),
          });
        } else {
          // Second call succeeds
          route.fulfill({
            status: 200,
            contentType: "application/json",
            body: JSON.stringify({
              overview: {
                totalRevenue: 1000000,
                totalOrders: 100,
                totalCustomers: 50,
                totalProducts: 25,
                revenueGrowth: 10,
                ordersGrowth: 5,
                customersGrowth: 15,
              },
              monthlyRevenue: [],
              topProducts: [],
              topCategories: [],
              ordersByStatus: [],
              customerStats: {
                newCustomers: 10,
                returningCustomers: 40,
                averageOrderValue: 500000,
              },
            }),
          });
        }
      });

      // Go to analytics page
      await page.goto("/admin/analytics");

      // Should show error first
      await expect(page.getByText("Có lỗi xảy ra")).toBeVisible();

      // Click retry button
      await page.getByRole("button", { name: "Thử lại" }).click();

      // Should show success after retry
      await expect(page.getByText("1.000.000 ₫")).toBeVisible();
    });
  });

  test.describe("Attributes Page", () => {
    test("should display attributes page", async ({ page }) => {
      // Login as admin
      await loginPage.loginAsAdmin();

      // Go to attributes page
      await page.goto("/admin/attributes");

      // Should display attributes page
      await expect(page.getByText("Quản lý thuộc tính")).toBeVisible();
      await expect(page.getByText("Tính năng đang phát triển")).toBeVisible();
    });

    test("should show development notice", async ({ page }) => {
      // Login as admin
      await loginPage.loginAsAdmin();

      // Go to attributes page
      await page.goto("/admin/attributes");

      // Should show development notice
      await expect(page.getByText("Tính năng đang phát triển")).toBeVisible();
      await expect(
        page.getByText("Dữ liệu hiển thị dưới đây chỉ mang tính chất demo")
      ).toBeVisible();
    });

    test("should display placeholder attributes", async ({ page }) => {
      // Login as admin
      await loginPage.loginAsAdmin();

      // Go to attributes page
      await page.goto("/admin/attributes");

      // Should display placeholder attributes
      await expect(page.getByText("Màu sắc")).toBeVisible();
      await expect(page.getByText("Kích thước")).toBeVisible();
      await expect(page.getByText("Chất liệu")).toBeVisible();
    });

    test("should show development toast when clicking buttons", async ({
      page,
    }) => {
      // Login as admin
      await loginPage.loginAsAdmin();

      // Go to attributes page
      await page.goto("/admin/attributes");

      // Click create button
      await page.getByRole("button", { name: "Thêm thuộc tính" }).click();

      // Should show development toast
      await expect(
        page.getByText("Tính năng này đang được phát triển")
      ).toBeVisible();
    });

    test("should allow search functionality", async ({ page }) => {
      // Login as admin
      await loginPage.loginAsAdmin();

      // Go to attributes page
      await page.goto("/admin/attributes");

      // Wait for attributes to load
      await expect(page.getByText("Màu sắc")).toBeVisible();

      // Search for specific attribute
      await page.locator('input[placeholder*="Tìm kiếm"]').fill("Màu");

      // Should show filtered results
      await expect(page.getByText("Màu sắc")).toBeVisible();
      await expect(page.getByText("Kích thước")).not.toBeVisible();
    });
  });

  test.describe("Navigation", () => {
    test("should navigate between admin pages correctly", async ({ page }) => {
      // Login as admin
      await loginPage.loginAsAdmin();

      // Start at dashboard
      await dashboardPage.goto();
      await dashboardPage.expectDashboardVisible();

      // Navigate to analytics
      await page.getByText("Báo cáo").click();
      await expect(page).toHaveURL("/admin/analytics");

      // Navigate to attributes
      await page.getByText("Thuộc tính").click();
      await expect(page).toHaveURL("/admin/attributes");

      // Navigate back to dashboard
      await page.getByText("Dashboard").click();
      await expect(page).toHaveURL("/admin");
    });
  });
});
