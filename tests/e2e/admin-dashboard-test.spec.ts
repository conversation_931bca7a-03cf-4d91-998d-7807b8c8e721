import { test, expect } from "@playwright/test";

test("Admin login to dashboard", async ({ page }) => {
  test.setTimeout(20000);

  console.log("🚀 Testing admin login to main dashboard...");

  await page.goto("/admin/auth/signin");

  await page.fill("#email", "<EMAIL>");
  await page.fill("#password", "admin123");

  // Click submit and wait for redirect
  await page.click('button[type="submit"]');

  // Wait for redirect with timeout
  await page.waitForTimeout(3000);

  console.log("✅ Final URL:", page.url());

  // Verify we're on admin dashboard (not auth page)
  expect(page.url()).toBe("http://localhost:3000/admin");

  // Check page content
  const title = await page.title();
  console.log("📄 Page title:", title);

  await page.screenshot({ path: "admin-dashboard-success.png" });

  console.log("🎉 Admin login to dashboard SUCCESS!");
});
