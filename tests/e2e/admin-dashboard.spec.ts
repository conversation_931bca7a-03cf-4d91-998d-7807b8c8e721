import { expect, test } from "@playwright/test";
import { AdminLoginPage } from "./pages/admin-login.page";
import { AdminDashboardPage } from "./pages/admin-dashboard.page";

test.describe("Admin Dashboard", () => {
  let loginPage: AdminLoginPage;
  let dashboardPage: AdminDashboardPage;

  test.beforeEach(async ({ page }) => {
    loginPage = new AdminLoginPage(page);
    dashboardPage = new AdminDashboardPage(page);
    
    // Login as admin before each test
    await loginPage.loginAsAdmin();
  });

  test.describe("Dashboard Display", () => {
    test("should display dashboard page correctly", async ({ page }) => {
      await dashboardPage.goto();
      await dashboardPage.expectDashboardVisible();
    });

    test("should show dashboard statistics", async ({ page }) => {
      await dashboardPage.goto();
      
      // Check for statistics cards
      await expect(page.getByText("Tổng doanh thu")).toBeVisible();
      await expect(page.getByText("Tổng đơn hàng")).toBeVisible();
      await expect(page.getByText("Tổng khách hàng")).toBeVisible();
    });

    test("should display recent orders section", async ({ page }) => {
      await dashboardPage.goto();
      
      await expect(page.getByText("Đơn hàng gần đây")).toBeVisible();
      await expect(page.locator('table')).toBeVisible();
    });

    test("should display top products section", async ({ page }) => {
      await dashboardPage.goto();
      
      await expect(page.getByText("Sản phẩm bán chạy")).toBeVisible();
    });

    test("should display monthly comparison", async ({ page }) => {
      await dashboardPage.goto();
      
      await expect(page.getByText("So sánh với tháng trước")).toBeVisible();
    });
  });

  test.describe("Dashboard Navigation", () => {
    test("should navigate to products page", async ({ page }) => {
      await dashboardPage.goto();
      
      await page.getByText("Sản phẩm").click();
      await expect(page).toHaveURL("/admin/products");
    });

    test("should navigate to orders page", async ({ page }) => {
      await dashboardPage.goto();
      
      await page.getByText("Đơn hàng").click();
      await expect(page).toHaveURL("/admin/orders");
    });

    test("should navigate to categories page", async ({ page }) => {
      await dashboardPage.goto();
      
      await page.getByText("Danh mục").click();
      await expect(page).toHaveURL("/admin/categories");
    });

    test("should navigate to analytics page", async ({ page }) => {
      await dashboardPage.goto();
      
      await page.getByText("Báo cáo").click();
      await expect(page).toHaveURL("/admin/analytics");
    });
  });

  test.describe("Dashboard Interactions", () => {
    test("should view details of a recent order", async ({ page }) => {
      await dashboardPage.goto();
      
      // Click on first order in recent orders table
      await page.locator('table tbody tr').first().click();
      
      // Should navigate to order details
      await expect(page.url()).toContain("/admin/orders/");
    });

    test("should refresh dashboard data", async ({ page }) => {
      await dashboardPage.goto();
      
      // Find and click refresh button if it exists
      const refreshButton = page.getByRole("button", { name: "Làm mới" });
      if (await refreshButton.isVisible()) {
        await refreshButton.click();
        // Check for loading indicator or updated timestamp
        await expect(page.getByText("Đã cập nhật")).toBeVisible();
      }
    });

    test("should toggle sidebar", async ({ page }) => {
      await dashboardPage.goto();
      
      // Click sidebar collapse button
      await dashboardPage.sidebarCollapseButton.click();
      
      // Verify sidebar is collapsed
      await expect(page.locator('.collapsed-sidebar, [data-collapsed="true"]')).toBeVisible();
    });
  });

  test.describe("Dashboard Data", () => {
    test("should display correct total revenue", async ({ page }) => {
      await dashboardPage.goto();
      
      // Check revenue card has a value
      const revenueText = await page.getByText("Tổng doanh thu").locator('..').textContent();
      expect(revenueText).toMatch(/\d/); // Contains at least one digit
    });

    test("should display correct order count", async ({ page }) => {
      await dashboardPage.goto();
      
      // Check orders card has a value
      const ordersText = await page.getByText("Tổng đơn hàng").locator('..').textContent();
      expect(ordersText).toMatch(/\d/); // Contains at least one digit
    });

    test("should display correct customer count", async ({ page }) => {
      await dashboardPage.goto();
      
      // Check customers card has a value
      const customersText = await page.getByText("Tổng khách hàng").locator('..').textContent();
      expect(customersText).toMatch(/\d/); // Contains at least one digit
    });
  });

  test.describe("Dashboard Permissions", () => {
    test("should allow admin to access dashboard", async ({ page }) => {
      // Already logged in as admin in beforeEach
      await dashboardPage.goto();
      await dashboardPage.expectDashboardVisible();
    });

    test("should allow moderator to access dashboard", async ({ page }) => {
      // Logout first
      await dashboardPage.logoutViaSidebar();
      
      // Login as moderator
      await loginPage.loginAsModerator();
      
      // Check dashboard access
      await dashboardPage.goto();
      await dashboardPage.expectDashboardVisible();
    });

    test("should redirect to login if not authenticated", async ({ page }) => {
      // Logout first
      await dashboardPage.logoutViaSidebar();
      
      // Try to access dashboard without login
      await page.goto("/admin");
      
      // Should redirect to login
      await expect(page).toHaveURL("/admin/auth/signin");
    });
  });

  test.describe("Responsive Design", () => {
    test("should display correctly on mobile", async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      
      await dashboardPage.goto();
      await dashboardPage.expectDashboardVisible();
    });

    test("should display correctly on tablet", async ({ page }) => {
      // Set tablet viewport
      await page.setViewportSize({ width: 768, height: 1024 });
      
      await dashboardPage.goto();
      await dashboardPage.expectDashboardVisible();
    });
  });
});
