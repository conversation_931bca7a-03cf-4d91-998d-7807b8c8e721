import { test, expect } from "@playwright/test";
import { AdminLoginPage } from "./pages/admin-login.page";
import { AdminDashboardPage } from "./pages/admin-dashboard.page";
import { ensureLoggedOut } from "./helpers/auth.helper";

test.describe("Admin Dashboard Logout", () => {
  let loginPage: AdminLoginPage;
  let dashboardPage: AdminDashboardPage;

  test.beforeEach(async ({ page }) => {
    loginPage = new AdminLoginPage(page);
    dashboardPage = new AdminDashboardPage(page);

    await ensureLoggedOut(page);

    // Login to admin dashboard
    await loginPage.goto();
    await loginPage.loginAsAdmin();
    await dashboardPage.expectDashboardVisible();
  });

  test("should logout via header user dropdown", async ({ page }) => {
    await dashboardPage.logoutViaDropdown();

    // Verify redirected to login page
    await expect(page).toHaveURL("/admin/auth/signin");
    await expect(page.getByText("Admin Portal")).toBeVisible();
  });

  test("should logout via sidebar button", async ({ page }) => {
    // Skip if sidebar logout button is not visible
    const sidebarLogoutButton = page.locator(
      'button[data-testid="logout-button"]:has-text("Đăng xuất")'
    );

    if (await sidebarLogoutButton.isVisible()) {
      await dashboardPage.logoutViaSidebar();

      // Verify redirected to login page
      await expect(page).toHaveURL("/admin/auth/signin");
      await expect(page.getByText("Admin Portal")).toBeVisible();
    } else {
      test.skip(true, "Sidebar logout button not visible in current layout");
    }
  });

  test("should clear session and prevent admin access after logout", async () => {
    // Logout
    await dashboardPage.logoutViaDropdown();

    // Verify session cleared
    await dashboardPage.verifyCookieCleared();

    // Try to access admin pages - should all redirect to login
    await dashboardPage.verifyAdminAccessDenied();
  });

  test("should maintain logout functionality across admin pages", async ({
    page,
  }) => {
    // Test logout from different admin pages
    const testPages = [
      { path: "/products", name: "Products" },
      { path: "/users", name: "Users" },
      { path: "", name: "Dashboard" }, // Root admin page
    ];

    for (const testPage of testPages) {
      // Navigate to the admin page
      await dashboardPage.navigateToAdminPage(testPage.path);

      // Logout should work from any admin page
      await dashboardPage.openUserDropdown();

      // Verify logout button is present
      await expect(dashboardPage.logoutButtonInDropdown).toBeVisible();
      await expect(dashboardPage.logoutButtonInDropdown).toContainText(
        "Đăng xuất"
      );

      await dashboardPage.logoutButtonInDropdown.click();

      // Should redirect to login
      await expect(page).toHaveURL("/admin/auth/signin");

      // Login again for next iteration (except last one)
      if (testPages.indexOf(testPage) < testPages.length - 1) {
        await loginPage.loginAsAdmin();
        await dashboardPage.expectDashboardVisible();
      }
    }
  });

  test("should handle logout with network errors gracefully", async ({
    page,
  }) => {
    // Intercept logout API and make it fail
    await page.route("/api/admin-logout", (route) => {
      route.fulfill({
        status: 500,
        contentType: "application/json",
        body: JSON.stringify({ error: "Server error" }),
      });
    });

    // Logout should still work due to fallback
    await dashboardPage.logoutViaDropdown();

    // Should still redirect to login page
    await expect(page).toHaveURL("/admin/auth/signin");
    await expect(page.getByText("Admin Portal")).toBeVisible();

    // Should not be able to access admin pages
    await page.goto("/admin");
    await expect(page).toHaveURL("/admin/auth/signin");
  });

  test("should show logout options consistently", async () => {
    // Verify logout button exists and has correct text
    await dashboardPage.expectLogoutButtonsVisible();

    // Test dropdown multiple times
    for (let i = 0; i < 3; i++) {
      await dashboardPage.openUserDropdown();
      await expect(dashboardPage.logoutButtonInDropdown).toBeVisible();
      await expect(dashboardPage.logoutButtonInDropdown).toContainText(
        "Đăng xuất"
      );
      await dashboardPage.closeUserDropdown();
    }
  });

  test("should logout in shared browser context", async ({ context }) => {
    // Open new tab with same context
    const newPage = await context.newPage();

    // New tab should have access to admin (shared session)
    await newPage.goto("/admin");
    await expect(newPage).toHaveURL("/admin");
    await expect(newPage.getByText("Dashboard")).toBeVisible();

    // Logout in original tab
    await dashboardPage.logoutViaDropdown();

    // New tab should lose access after logout
    await newPage.reload();
    await expect(newPage).toHaveURL("/admin/auth/signin");

    await newPage.close();
  });
});
