import { expect, test } from "@playwright/test";
import { AdminLoginPage } from "./pages/admin-login.page";
import { AdminProfilePage } from "./pages/admin-profile.page";

test.describe("Admin Profile Management", () => {
  let loginPage: AdminLoginPage;
  let profilePage: AdminProfilePage;

  test.beforeEach(async ({ page }) => {
    loginPage = new AdminLoginPage(page);
    profilePage = new AdminProfilePage(page);
    
    // Login as admin before each test
    await loginPage.loginAsAdmin();
  });

  test.describe("Profile Information Display", () => {
    test("should display profile page correctly", async ({ page }) => {
      await profilePage.goto();
      await profilePage.expectProfileFormVisible();
    });

    test("should show current admin information", async ({ page }) => {
      await profilePage.goto();
      
      // Verify default admin data is displayed
      await profilePage.expectProfileData({
        name: "Admin User",
        email: "<EMAIL>"
      });
    });

    test("should display password change form", async ({ page }) => {
      await profilePage.goto();
      await profilePage.expectPasswordFormVisible();
    });

    test("should display security settings", async ({ page }) => {
      await profilePage.goto();
      await profilePage.expectSecuritySettingsVisible();
    });
  });

  test.describe("Profile Information Update", () => {
    test("should update profile name successfully", async ({ page }) => {
      await profilePage.goto();
      
      const newName = "Updated Admin Name";
      await profilePage.updateProfile({ name: newName });
      
      await profilePage.expectSuccessMessage("Cập nhật thông tin thành công");
      await profilePage.expectProfileData({ name: newName });
    });

    test("should update profile phone successfully", async ({ page }) => {
      await profilePage.goto();
      
      const newPhone = "0123456789";
      await profilePage.updateProfile({ phone: newPhone });
      
      await profilePage.expectSuccessMessage();
      await profilePage.expectProfileData({ phone: newPhone });
    });

    test("should update multiple profile fields", async ({ page }) => {
      await profilePage.goto();
      
      const updates = {
        name: "New Admin Name",
        phone: "0987654321"
      };
      
      await profilePage.updateProfile(updates);
      
      await profilePage.expectSuccessMessage();
      await profilePage.expectProfileData(updates);
    });

    test("should validate required fields", async ({ page }) => {
      await profilePage.goto();
      
      // Try to clear required name field
      await profilePage.updateProfile({ name: "" });
      
      await profilePage.expectErrorMessage("Tên không được để trống");
    });

    test("should validate email format", async ({ page }) => {
      await profilePage.goto();
      
      await profilePage.updateProfile({ email: "invalid-email" });
      
      await profilePage.expectErrorMessage("Email không hợp lệ");
    });
  });

  test.describe("Password Change", () => {
    test("should change password successfully", async ({ page }) => {
      await profilePage.goto();
      
      await profilePage.changePassword("admin123", "newpassword123");
      
      await profilePage.expectSuccessMessage("Đổi mật khẩu thành công");
    });

    test("should validate current password", async ({ page }) => {
      await profilePage.goto();
      
      await profilePage.changePassword("wrongpassword", "newpassword123");
      
      await profilePage.expectErrorMessage("Mật khẩu hiện tại không đúng");
    });

    test("should validate password confirmation", async ({ page }) => {
      await profilePage.goto();
      
      await profilePage.changePassword("admin123", "newpassword123", "differentpassword");
      
      await profilePage.expectErrorMessage("Mật khẩu xác nhận không khớp");
    });

    test("should validate password strength", async ({ page }) => {
      await profilePage.goto();
      
      await profilePage.changePassword("admin123", "123");
      
      await profilePage.expectErrorMessage("Mật khẩu phải có ít nhất 6 ký tự");
    });

    test("should require current password", async ({ page }) => {
      await profilePage.goto();
      
      await profilePage.changePassword("", "newpassword123");
      
      await profilePage.expectErrorMessage("Vui lòng nhập mật khẩu hiện tại");
    });
  });

  test.describe("Security Settings", () => {
    test("should display two-factor authentication option", async ({ page }) => {
      await profilePage.goto();
      
      await expect(profilePage.activateTwoFactorButton).toBeVisible();
      await expect(page.getByText("Xác thực hai yếu tố")).toBeVisible();
    });

    test("should activate two-factor authentication", async ({ page }) => {
      await profilePage.goto();
      
      await profilePage.activateTwoFactor();
      
      // This would depend on the actual 2FA implementation
      // For now, just check that the action was triggered
      await expect(page.getByText("Kích hoạt 2FA")).toBeVisible();
    });
  });

  test.describe("Form Validation", () => {
    test("should prevent submission with invalid data", async ({ page }) => {
      await profilePage.goto();
      
      // Try to submit with invalid email
      await profilePage.nameInput.fill("Valid Name");
      await profilePage.emailInput.fill("invalid-email-format");
      await profilePage.saveProfileButton.click();
      
      // Should show validation error
      await profilePage.expectErrorMessage();
    });

    test("should show field-specific validation messages", async ({ page }) => {
      await profilePage.goto();
      
      // Clear required field
      await profilePage.nameInput.clear();
      await profilePage.saveProfileButton.click();
      
      // Check HTML5 validation
      const nameValidity = await profilePage.nameInput.evaluate(
        (el: HTMLInputElement) => el.validity.valid
      );
      expect(nameValidity).toBe(false);
    });
  });

  test.describe("Navigation and UI", () => {
    test("should navigate to profile from admin header", async ({ page }) => {
      await page.goto("/admin");
      
      // Click on user dropdown in header
      await page.locator('button:has(div:has-text("Admin"))').click();
      await page.getByText("Thông tin cá nhân").click();
      
      await expect(page).toHaveURL("/admin/profile");
      await profilePage.expectProfileFormVisible();
    });

    test("should cancel profile updates", async ({ page }) => {
      await profilePage.goto();
      
      const originalName = await profilePage.nameInput.inputValue();
      
      // Make changes
      await profilePage.nameInput.fill("Changed Name");
      
      // Cancel
      await profilePage.cancelProfileButton.click();
      
      // Should revert changes
      await expect(profilePage.nameInput).toHaveValue(originalName);
    });

    test("should be responsive on mobile", async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await profilePage.goto();
      
      await profilePage.expectProfileFormVisible();
      await profilePage.expectPasswordFormVisible();
    });
  });

  test.describe("Accessibility", () => {
    test("should have proper form labels", async ({ page }) => {
      await profilePage.goto();
      
      await expect(page.getByText("Họ và tên")).toBeVisible();
      await expect(page.getByText("Email")).toBeVisible();
      await expect(page.getByText("Số điện thoại")).toBeVisible();
    });

    test("should be keyboard navigable", async ({ page }) => {
      await profilePage.goto();
      
      // Tab through form elements
      await page.keyboard.press("Tab");
      await expect(profilePage.nameInput).toBeFocused();
      
      await page.keyboard.press("Tab");
      await expect(profilePage.emailInput).toBeFocused();
      
      await page.keyboard.press("Tab");
      await expect(profilePage.phoneInput).toBeFocused();
    });
  });
});
