import { test, expect } from "@playwright/test";

test.describe("Quick Admin Test", () => {
  test("should access login page", async ({ page }) => {
    await page.goto("/admin/auth/signin");
    await expect(page.getByText("Admin Portal")).toBeVisible({
      timeout: 10000,
    });
  });

  test("should redirect admin to login when not authenticated", async ({
    page,
  }) => {
    await page.goto("/admin");
    await expect(page).toHaveURL("/admin/auth/signin", { timeout: 10000 });
  });
});
