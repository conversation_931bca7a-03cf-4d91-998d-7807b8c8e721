import { test, expect } from "@playwright/test";
import { AdminLoginPage } from "./pages/admin-login.page";
import { ensureLoggedOut } from "./helpers/auth.helper";

test.describe("Admin Dashboard Logout Features", () => {
  let loginPage: AdminLoginPage;

  test.beforeEach(async ({ page }) => {
    loginPage = new AdminLoginPage(page);
    await ensureLoggedOut(page);

    // Login to admin dashboard for logout tests
    await loginPage.goto();
    await loginPage.loginAsAdmin();
    await expect(page).toHaveURL("/admin");
    await expect(page.getByText("Dashboard")).toBeVisible();
  });

  test.describe("Header Dropdown Logout", () => {
    test("should find and click user dropdown", async ({ page }) => {
      // Find user dropdown trigger button
      const userDropdownTrigger = page.locator(
        'button:has(div:has-text("Admin"))'
      );
      await expect(userDropdownTrigger).toBeVisible();

      // Click to open dropdown
      await userDropdownTrigger.click();

      // Wait for dropdown to appear
      await page.waitForTimeout(300);

      // Verify dropdown content is visible
      await expect(page.getByText("Tài khoản")).toBeVisible();
      await expect(page.getByText("Thông tin cá nhân")).toBeVisible();
      await expect(page.getByText("Đăng xuất")).toBeVisible();
    });

    test("should logout via header user dropdown", async ({ page }) => {
      // Open user dropdown
      const userDropdownTrigger = page.locator(
        'button:has(div:has-text("Admin"))'
      );
      await userDropdownTrigger.click();
      await page.waitForTimeout(300);

      // Click logout button in dropdown
      const logoutMenuItem = page.locator('[data-testid="logout-button"]');
      await expect(logoutMenuItem).toBeVisible();
      await logoutMenuItem.click();

      // Should redirect to login page
      await expect(page).toHaveURL("/admin/auth/signin");
      await expect(page.getByText("Admin Portal")).toBeVisible();
      await expect(
        page.getByText("Đăng nhập vào hệ thống quản trị NS Shop")
      ).toBeVisible();
    });

    test("should handle multiple rapid dropdown clicks", async ({ page }) => {
      // Open and close dropdown multiple times rapidly
      const userDropdownTrigger = page.locator(
        'button:has(div:has-text("Admin"))'
      );

      for (let i = 0; i < 3; i++) {
        await userDropdownTrigger.click();
        await page.waitForTimeout(100);

        // Click outside to close dropdown
        await page.click("body");
        await page.waitForTimeout(100);
      }

      // Final click and logout
      await userDropdownTrigger.click();
      await page.waitForTimeout(300);

      const logoutMenuItem = page.locator('[data-testid="logout-button"]');
      await expect(logoutMenuItem).toBeVisible();
      await logoutMenuItem.click();

      await expect(page).toHaveURL("/admin/auth/signin");
    });
  });

  test.describe("Sidebar Logout", () => {
    test("should logout via sidebar button", async ({ page }) => {
      // Look for logout button in sidebar
      const sidebarLogoutButton = page.locator(
        'button[data-testid="logout-button"]:has-text("Đăng xuất")'
      );

      // If sidebar is collapsed, try to expand it first
      const expandButton = page.locator('button:has-text("☰")').first();
      if (await expandButton.isVisible()) {
        await expandButton.click();
        await page.waitForTimeout(300);
      }

      await expect(sidebarLogoutButton).toBeVisible();
      await sidebarLogoutButton.click();

      // Should redirect to login page
      await expect(page).toHaveURL("/admin/auth/signin");
      await expect(page.getByText("Admin Portal")).toBeVisible();
    });

    test("should show logout button in collapsed sidebar", async ({ page }) => {
      // Try to find and click sidebar collapse button
      const collapseButton = page
        .locator('button[aria-label*="collapse"], button[aria-label*="toggle"]')
        .first();

      if (await collapseButton.isVisible()) {
        await collapseButton.click();
        await page.waitForTimeout(300);

        // In collapsed state, logout button should still be clickable
        const collapsedLogoutButton = page
          .locator('button[data-testid="logout-button"]')
          .first();
        if (await collapsedLogoutButton.isVisible()) {
          await collapsedLogoutButton.click();
          await expect(page).toHaveURL("/admin/auth/signin");
        }
      }
    });
  });

  test.describe("Logout Behavior Validation", () => {
    test("should clear admin session cookie", async ({ page }) => {
      // Logout via any method
      const userDropdownTrigger = page.locator(
        'button:has(div:has-text("Admin"))'
      );
      await userDropdownTrigger.click();
      await page.waitForTimeout(300);

      const logoutMenuItem = page.locator('[data-testid="logout-button"]');
      await logoutMenuItem.click();
      await expect(page).toHaveURL("/admin/auth/signin");

      // Check cookies
      const cookies = await page.context().cookies();
      const adminSessionCookie = cookies.find(
        (cookie) => cookie.name === "admin-session"
      );

      // Cookie should be cleared (empty value or expired)
      if (adminSessionCookie) {
        expect(adminSessionCookie.value).toBe("");
      }
    });

    test("should prevent admin access after logout", async ({ page }) => {
      // Logout first
      const userDropdownTrigger = page.locator(
        'button:has(div:has-text("Admin"))'
      );
      await userDropdownTrigger.click();
      await page.waitForTimeout(300);

      const logoutMenuItem = page.locator('[data-testid="logout-button"]');
      await logoutMenuItem.click();
      await expect(page).toHaveURL("/admin/auth/signin");

      // Try to access various admin pages
      const adminPages = [
        "/admin",
        "/admin/products",
        "/admin/users",
        "/admin/orders",
        "/admin/settings",
      ];

      for (const adminPage of adminPages) {
        await page.goto(adminPage);

        // Should always redirect to login
        await expect(page).toHaveURL("/admin/auth/signin");
        await expect(page.getByText("Admin Portal")).toBeVisible();
      }
    });

    test("should handle logout in new browser tab", async ({
      page,
      context,
    }) => {
      // Open new tab
      const newPage = await context.newPage();

      // Login in new tab should work (shared session)
      await newPage.goto("/admin");
      await expect(newPage).toHaveURL("/admin");

      // Logout in original tab
      const userDropdownTrigger = page.locator(
        'button:has(div:has-text("Admin"))'
      );
      await userDropdownTrigger.click();
      await page.waitForTimeout(300);

      const logoutMenuItem = page.locator('[data-testid="logout-button"]');
      await logoutMenuItem.click();
      await expect(page).toHaveURL("/admin/auth/signin");

      // New tab should also lose access
      await newPage.reload();
      await expect(newPage).toHaveURL("/admin/auth/signin");

      await newPage.close();
    });
  });

  test.describe("Logout Error Handling", () => {
    test("should handle logout API failure gracefully", async ({ page }) => {
      // Intercept logout API and make it fail
      await page.route("/api/admin-logout", (route) => {
        route.fulfill({
          status: 500,
          contentType: "application/json",
          body: JSON.stringify({ error: "Server error" }),
        });
      });

      // Logout should still work due to fallback
      const userDropdownTrigger = page.locator(
        'button:has(div:has-text("Admin"))'
      );
      await userDropdownTrigger.click();
      await page.waitForTimeout(300);

      const logoutMenuItem = page.locator('[data-testid="logout-button"]');
      await logoutMenuItem.click();

      // Should still redirect to login (fallback behavior)
      await expect(page).toHaveURL("/admin/auth/signin");
      await expect(page.getByText("Admin Portal")).toBeVisible();
    });

    test("should handle network timeout during logout", async ({ page }) => {
      // Intercept and delay logout API
      await page.route("/api/admin-logout", (route) => {
        // Delay response for 10 seconds (simulate timeout)
        setTimeout(() => {
          route.fulfill({
            status: 200,
            contentType: "application/json",
            body: JSON.stringify({ success: true }),
          });
        }, 10000);
      });

      // Logout should still work due to fallback mechanism
      const userDropdownTrigger = page.locator(
        'button:has(div:has-text("Admin"))'
      );
      await userDropdownTrigger.click();
      await page.waitForTimeout(300);

      const logoutMenuItem = page.locator('[data-testid="logout-button"]');
      await logoutMenuItem.click();

      // Should redirect to login even with API delay
      await expect(page).toHaveURL("/admin/auth/signin", { timeout: 15000 });
    });
  });

  test.describe("Logout UI/UX", () => {
    test("should show consistent logout button text", async ({ page }) => {
      // Check header dropdown logout text
      const userDropdownTrigger = page.locator(
        'button:has(div:has-text("Admin"))'
      );
      await userDropdownTrigger.click();
      await page.waitForTimeout(300);

      const headerLogoutButton = page
        .locator('[data-testid="logout-button"]')
        .first();
      await expect(headerLogoutButton).toContainText("Đăng xuất");

      // Close dropdown
      await page.click("body");
      await page.waitForTimeout(300);

      // Check sidebar logout text (if visible)
      const sidebarLogoutButton = page.locator(
        'button[data-testid="logout-button"]:has-text("Đăng xuất")'
      );
      if (await sidebarLogoutButton.isVisible()) {
        await expect(sidebarLogoutButton).toContainText("Đăng xuất");
      }
    });

    test("should have proper logout button styling", async ({ page }) => {
      // Open dropdown
      const userDropdownTrigger = page.locator(
        'button:has(div:has-text("Admin"))'
      );
      await userDropdownTrigger.click();
      await page.waitForTimeout(300);

      // Check logout button has logout icon
      const logoutButton = page.locator('[data-testid="logout-button"]');
      await expect(logoutButton).toBeVisible();

      // Should have LogOut icon (svg)
      const logoutIcon = logoutButton.locator("svg").first();
      await expect(logoutIcon).toBeVisible();
    });

    test("should maintain logout functionality across page navigation", async ({
      page,
    }) => {
      // Navigate to different admin pages
      const adminPages = [
        { url: "/admin/users", expectedText: "Users" },
        { url: "/admin/products", expectedText: "Products" },
        { url: "/admin", expectedText: "Dashboard" },
      ];

      for (const adminPage of adminPages) {
        await page.goto(adminPage.url);
        await page.waitForLoadState("networkidle");

        // Logout should work from any admin page
        const userDropdownTrigger = page.locator(
          'button:has(div:has-text("Admin"))'
        );
        if (await userDropdownTrigger.isVisible()) {
          await userDropdownTrigger.click();
          await page.waitForTimeout(300);

          const logoutMenuItem = page.locator('[data-testid="logout-button"]');
          if (await logoutMenuItem.isVisible()) {
            await logoutMenuItem.click();

            await expect(page).toHaveURL("/admin/auth/signin");

            // Login again for next iteration
            if (adminPages.indexOf(adminPage) < adminPages.length - 1) {
              await loginPage.loginAsAdmin();
              await expect(page).toHaveURL("/admin");
            }
            break;
          }
        }
      }
    });
  });
});
