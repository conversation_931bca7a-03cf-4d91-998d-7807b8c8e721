import { expect, test } from "@playwright/test";
import { AdminLoginPage } from "./pages/admin-login.page";
import { AdminAnalyticsPage } from "./pages/admin-analytics.page";

test.describe("Admin Analytics", () => {
  let loginPage: AdminLoginPage;
  let analyticsPage: AdminAnalyticsPage;

  test.beforeEach(async ({ page }) => {
    loginPage = new AdminLoginPage(page);
    analyticsPage = new AdminAnalyticsPage(page);
    
    // Login as admin before each test
    await loginPage.loginAsAdmin();
  });

  test.describe("Analytics Page Display", () => {
    test("should display analytics page correctly", async ({ page }) => {
      await analyticsPage.goto();
      await analyticsPage.expectAnalyticsPageVisible();
    });

    test("should display summary cards", async ({ page }) => {
      await analyticsPage.goto();
      await analyticsPage.expectSummaryCardsVisible();
    });

    test("should display revenue chart", async ({ page }) => {
      await analyticsPage.goto();
      await expect(analyticsPage.revenueChart).toBeVisible();
    });

    test("should display orders chart", async ({ page }) => {
      await analyticsPage.goto();
      await expect(analyticsPage.ordersChart).toBeVisible();
    });

    test("should display top products table", async ({ page }) => {
      await analyticsPage.goto();
      await analyticsPage.expectTopProductsTableVisible();
    });

    test("should display top customers table", async ({ page }) => {
      await analyticsPage.goto();
      await analyticsPage.expectTopCustomersTableVisible();
    });
  });

  test.describe("Date Range Filtering", () => {
    test("should filter by custom date range", async ({ page }) => {
      await analyticsPage.goto();
      
      const startDate = "2024-01-01";
      const endDate = "2024-01-31";
      
      await analyticsPage.filterByDateRange(startDate, endDate);
      await analyticsPage.expectDataInDateRange(startDate, endDate);
    });

    test("should filter by today", async ({ page }) => {
      await analyticsPage.goto();
      
      await analyticsPage.filterByPredefinedRange("today");
      await analyticsPage.waitForChartsToLoad();
      
      // Verify charts are updated
      await expect(analyticsPage.revenueChart).toBeVisible();
    });

    test("should filter by last 7 days", async ({ page }) => {
      await analyticsPage.goto();
      
      await analyticsPage.filterByPredefinedRange("last7days");
      await analyticsPage.waitForChartsToLoad();
      
      // Verify charts are updated
      await expect(analyticsPage.revenueChart).toBeVisible();
    });

    test("should filter by last 30 days", async ({ page }) => {
      await analyticsPage.goto();
      
      await analyticsPage.filterByPredefinedRange("last30days");
      await analyticsPage.waitForChartsToLoad();
      
      // Verify charts are updated
      await expect(analyticsPage.revenueChart).toBeVisible();
    });

    test("should filter by this month", async ({ page }) => {
      await analyticsPage.goto();
      
      await analyticsPage.filterByPredefinedRange("thisMonth");
      await analyticsPage.waitForChartsToLoad();
      
      // Verify charts are updated
      await expect(analyticsPage.revenueChart).toBeVisible();
    });
  });

  test.describe("Data Export", () => {
    test("should export report as PDF", async ({ page }) => {
      await analyticsPage.goto();
      
      // Start download
      const downloadPromise = page.waitForEvent('download');
      await analyticsPage.exportReport("pdf");
      const download = await downloadPromise;
      
      // Verify download
      expect(download.suggestedFilename()).toContain('.pdf');
    });

    test("should export report as Excel", async ({ page }) => {
      await analyticsPage.goto();
      
      // Start download
      const downloadPromise = page.waitForEvent('download');
      await analyticsPage.exportReport("excel");
      const download = await downloadPromise;
      
      // Verify download
      expect(download.suggestedFilename()).toMatch(/\.(xlsx|xls)$/);
    });

    test("should export report as CSV", async ({ page }) => {
      await analyticsPage.goto();
      
      // Start download
      const downloadPromise = page.waitForEvent('download');
      await analyticsPage.exportReport("csv");
      const download = await downloadPromise;
      
      // Verify download
      expect(download.suggestedFilename()).toContain('.csv');
    });
  });

  test.describe("Print Functionality", () => {
    test("should open print dialog", async ({ page }) => {
      await analyticsPage.goto();
      
      // Mock print dialog
      await page.evaluate(() => {
        window.print = () => console.log('Print dialog opened');
      });
      
      await analyticsPage.printReport();
      
      // Verify print was triggered (this is a basic check)
      // In a real scenario, you might check for print CSS or other indicators
    });
  });

  test.describe("Data Refresh", () => {
    test("should refresh analytics data", async ({ page }) => {
      await analyticsPage.goto();
      
      // Get initial data
      const initialRevenue = await analyticsPage.totalRevenueCard.textContent();
      
      // Refresh data
      await analyticsPage.refreshData();
      
      // Verify refresh completed
      await expect(analyticsPage.revenueChart).toBeVisible();
    });

    test("should show loading indicator during refresh", async ({ page }) => {
      await analyticsPage.goto();
      
      // Intercept API calls to delay response
      await page.route('/api/admin/analytics', async route => {
        await new Promise(resolve => setTimeout(resolve, 2000));
        route.continue();
      });
      
      await analyticsPage.refreshButton.click();
      
      // Check for loading indicator
      await expect(analyticsPage.loadingIndicator).toBeVisible();
    });
  });

  test.describe("Error Handling", () => {
    test("should handle API errors gracefully", async ({ page }) => {
      // Intercept and fail API calls
      await page.route('/api/admin/analytics', route => {
        route.abort('failed');
      });
      
      await analyticsPage.goto();
      
      // Should show error message
      await analyticsPage.expectErrorMessage("Không thể tải dữ liệu");
    });

    test("should handle network timeout", async ({ page }) => {
      // Intercept and delay API calls
      await page.route('/api/admin/analytics', async route => {
        await new Promise(resolve => setTimeout(resolve, 30000));
        route.continue();
      });
      
      await analyticsPage.goto();
      
      // Should show timeout error
      await analyticsPage.expectErrorMessage();
    });
  });

  test.describe("Permissions", () => {
    test("should allow admin to access analytics", async ({ page }) => {
      // Already logged in as admin
      await analyticsPage.goto();
      await analyticsPage.expectAnalyticsPageVisible();
    });

    test("should allow moderator to access analytics", async ({ page }) => {
      // Logout and login as moderator
      await page.goto("/admin/auth/signin");
      await loginPage.loginAsModerator();
      
      await analyticsPage.goto();
      await analyticsPage.expectAnalyticsPageVisible();
    });
  });

  test.describe("Responsive Design", () => {
    test("should display correctly on mobile", async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      
      await analyticsPage.goto();
      await analyticsPage.expectAnalyticsPageVisible();
    });

    test("should display correctly on tablet", async ({ page }) => {
      await page.setViewportSize({ width: 768, height: 1024 });
      
      await analyticsPage.goto();
      await analyticsPage.expectAnalyticsPageVisible();
    });
  });
});
