import { expect, test } from "@playwright/test";
import { ensureLoggedOut } from "./helpers/auth.helper";
import { AdminLoginPage } from "./pages/admin-login.page";

test.describe("Admin Login", () => {
  let loginPage: AdminLoginPage;

  test.beforeEach(async ({ page }) => {
    loginPage = new AdminLoginPage(page);
    await ensureLoggedOut(page);
  });

  test.describe("Login Form Display", () => {
    test("should display login form correctly", async ({ page }) => {
      await loginPage.goto();
      await loginPage.expectLoginFormVisible();
    });

    test("should show demo credentials", async ({ page }) => {
      await loginPage.goto();
      await loginPage.expectDemoCredentialsVisible();
    });

    test("should have proper form validation attributes", async ({ page }) => {
      await loginPage.goto();
      await expect(loginPage.emailInput).toHaveAttribute("type", "email");
      await expect(loginPage.emailInput).toHaveAttribute("required");
      await expect(loginPage.passwordInput).toHaveAttribute("type", "password");
      await expect(loginPage.passwordInput).toHaveAttribute("required");
    });
  });

  test.describe("Successful Login", () => {
    test("should login successfully with admin credentials", async ({
      page,
    }) => {
      await loginPage.goto();
      await loginPage.loginAsAdmin();

      // Should be redirected to admin dashboard
      await expect(page).toHaveURL("/admin");
      await expect(page.getByText("Dashboard")).toBeVisible();
    });

    test("should login successfully with moderator credentials", async ({
      page,
    }) => {
      await loginPage.goto();
      await loginPage.loginAsModerator();

      // Should be redirected to admin dashboard
      await expect(page).toHaveURL("/admin");
      await expect(page.getByText("Dashboard")).toBeVisible();
    });

    test("should update last login time after successful login", async ({
      page,
    }) => {
      await loginPage.goto();

      // Record time before login
      const beforeLogin = new Date();

      await loginPage.loginAsAdmin();
      await expect(page).toHaveURL("/admin");

      // Verify login was recorded (this would need API verification)
      // For now, we just verify successful navigation
      await expect(page.getByText("Dashboard")).toBeVisible();
    });
  });

  test.describe("Failed Login", () => {
    test("should show error for invalid email", async ({ page }) => {
      await loginPage.goto();
      await loginPage.login("<EMAIL>", "password123");
      await loginPage.expectErrorMessage("Email hoặc mật khẩu không đúng");
    });

    test("should show error for invalid password", async ({ page }) => {
      await loginPage.goto();
      await loginPage.login("<EMAIL>", "wrongpassword");
      await loginPage.expectErrorMessage("Email hoặc mật khẩu không đúng");
    });

    test("should show error for empty credentials", async ({ page }) => {
      await loginPage.goto();
      await loginPage.expectFormValidation();
    });

    test("should show error for invalid email format", async ({ page }) => {
      await loginPage.goto();
      await loginPage.fillInvalidEmail();
    });

    test("should handle network errors gracefully", async ({ page }) => {
      // Intercept and fail the login request
      await page.route("/api/auth/callback/admin-credentials", (route) => {
        route.abort("failed");
      });

      await loginPage.goto();
      await loginPage.login("<EMAIL>", "admin123");

      // Should show generic error message
      await loginPage.expectErrorMessage("Có lỗi xảy ra khi đăng nhập");
    });
  });

  test.describe("Form Interaction", () => {
    test("should show loading state during login", async ({ page }) => {
      // Delay the login request to see loading state
      await page.route(
        "/api/auth/callback/admin-credentials",
        async (route) => {
          await new Promise((resolve) => setTimeout(resolve, 2000));
          route.continue();
        }
      );

      await loginPage.goto();
      await loginPage.emailInput.fill("<EMAIL>");
      await loginPage.passwordInput.fill("admin123");
      await loginPage.loginButton.click();

      await loginPage.expectLoadingState();
    });

    test("should allow form submission with Enter key", async ({ page }) => {
      await loginPage.goto();
      await loginPage.emailInput.fill("<EMAIL>");
      await loginPage.passwordInput.fill("admin123");
      await loginPage.passwordInput.press("Enter");

      await loginPage.waitForNavigation();
      await expect(page).toHaveURL("/admin");
    });

    test("should clear error message on new input", async ({ page }) => {
      await loginPage.goto();

      // First, trigger an error
      await loginPage.loginWithInvalidCredentials();
      await loginPage.waitForError();

      // Then start typing in email field
      await loginPage.emailInput.fill("a");

      // Error should be cleared (this depends on implementation)
      await expect(loginPage.errorMessage).not.toBeVisible();
    });
  });

  test.describe("Security", () => {
    test("should not login with inactive user account", async ({ page }) => {
      // This test assumes there's an inactive user in the database
      // You might need to create one in the global setup
      await loginPage.goto();
      await loginPage.login("<EMAIL>", "password123");
      await loginPage.expectErrorMessage("Email hoặc mật khẩu không đúng");
    });

    test("should prevent multiple rapid login attempts", async ({ page }) => {
      await loginPage.goto();

      // Make multiple rapid failed attempts
      for (let i = 0; i < 3; i++) {
        await loginPage.loginWithInvalidCredentials();
        await loginPage.waitForError();
      }

      // Should still show error (rate limiting would be server-side)
      await loginPage.expectErrorMessage("Email hoặc mật khẩu không đúng");
    });
  });

  test.describe("Accessibility", () => {
    test("should have proper ARIA labels and roles", async ({ page }) => {
      await loginPage.goto();

      await expect(loginPage.emailInput).toHaveAttribute("aria-label", "Email");
      await expect(loginPage.passwordInput).toHaveAttribute(
        "aria-label",
        "Mật khẩu"
      );
      await expect(loginPage.loginButton).toHaveAttribute("type", "submit");
    });

    test("should be keyboard navigable", async ({ page }) => {
      await loginPage.goto();

      // Tab through form elements
      await page.keyboard.press("Tab");
      await expect(loginPage.emailInput).toBeFocused();

      await page.keyboard.press("Tab");
      await expect(loginPage.passwordInput).toBeFocused();

      await page.keyboard.press("Tab");
      await expect(loginPage.loginButton).toBeFocused();
    });
  });
});
