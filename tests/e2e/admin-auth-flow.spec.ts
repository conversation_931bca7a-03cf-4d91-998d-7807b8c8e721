import { test, expect } from "@playwright/test";
import { AdminLoginPage } from "./pages/admin-login.page";
import { ensureLoggedOut } from "./helpers/auth.helper";

test.describe("Admin Authentication Flow", () => {
  let loginPage: AdminLoginPage;

  test.beforeEach(async ({ page }) => {
    loginPage = new AdminLoginPage(page);
    await ensureLoggedOut(page);
  });

  test.describe("Login Flow", () => {
    test("should login successfully and redirect to dashboard", async ({
      page,
    }) => {
      await loginPage.goto();
      await loginPage.loginAsAdmin();

      // Should be redirected to admin dashboard
      await expect(page).toHaveURL("/admin");
      await expect(page.getByText("Dashboard")).toBeVisible();

      // Should show admin content
      await expect(
        page.getByText("Tổng quan về hoạt động kinh doanh")
      ).toBeVisible();
    });

    test("should login as moderator and access dashboard", async ({ page }) => {
      await loginPage.goto();
      await loginPage.loginAsModerator();

      // Should be redirected to admin dashboard
      await expect(page).toHaveURL("/admin");
      await expect(page.getByText("Dashboard")).toBeVisible();
    });

    test("should stay logged in after page refresh", async ({ page }) => {
      await loginPage.goto();
      await loginPage.loginAsAdmin();

      // Verify logged in
      await expect(page).toHaveURL("/admin");

      // Refresh page
      await page.reload();

      // Should still be logged in and on dashboard
      await expect(page).toHaveURL("/admin");
      await expect(page.getByText("Dashboard")).toBeVisible();
    });

    test("should redirect to login if accessing admin without authentication", async ({
      page,
    }) => {
      // Try to access admin dashboard directly
      await page.goto("/admin");

      // Should be redirected to login
      await expect(page).toHaveURL("/admin/auth/signin");
      await expect(page.getByText("Admin Portal")).toBeVisible();
    });
  });

  test.describe("Logout Flow", () => {
    test("should logout from header dropdown", async ({ page }) => {
      // Login first
      await loginPage.goto();
      await loginPage.loginAsAdmin();
      await expect(page).toHaveURL("/admin");

      // Click on user dropdown
      await page.click(
        '[data-testid="user-dropdown"], .dropdown-trigger, button:has(svg)'
      );

      // Wait a bit for dropdown to appear
      await page.waitForTimeout(500);

      // Look for logout button in dropdown and click it
      const logoutButton = page
        .locator('[data-testid="logout-button"], button:has-text("Đăng xuất")')
        .first();
      await expect(logoutButton).toBeVisible();
      await logoutButton.click();

      // Should be redirected to login page
      await expect(page).toHaveURL("/admin/auth/signin");
      await expect(page.getByText("Admin Portal")).toBeVisible();
    });

    test("should logout from sidebar", async ({ page }) => {
      // Login first
      await loginPage.goto();
      await loginPage.loginAsAdmin();
      await expect(page).toHaveURL("/admin");

      // Look for logout button in sidebar
      const sidebarLogoutButton = page
        .locator(
          '[data-testid="logout-button"]:visible, button:has-text("Đăng xuất"):visible'
        )
        .first();
      await expect(sidebarLogoutButton).toBeVisible();
      await sidebarLogoutButton.click();

      // Should be redirected to login page
      await expect(page).toHaveURL("/admin/auth/signin");
      await expect(page.getByText("Admin Portal")).toBeVisible();
    });

    test("should not access admin pages after logout", async ({ page }) => {
      // Login first
      await loginPage.goto();
      await loginPage.loginAsAdmin();
      await expect(page).toHaveURL("/admin");

      // Logout
      const logoutButton = page
        .locator('[data-testid="logout-button"], button:has-text("Đăng xuất")')
        .first();
      await logoutButton.click();
      await expect(page).toHaveURL("/admin/auth/signin");

      // Try to access admin pages
      await page.goto("/admin");

      // Should be redirected back to login
      await expect(page).toHaveURL("/admin/auth/signin");
      await expect(page.getByText("Admin Portal")).toBeVisible();
    });

    test("should clear session after logout", async ({ page }) => {
      // Login first
      await loginPage.goto();
      await loginPage.loginAsAdmin();
      await expect(page).toHaveURL("/admin");

      // Logout
      const logoutButton = page
        .locator('[data-testid="logout-button"], button:has-text("Đăng xuất")')
        .first();
      await logoutButton.click();
      await expect(page).toHaveURL("/admin/auth/signin");

      // Check that admin-session cookie is cleared
      const cookies = await page.context().cookies();
      const adminSessionCookie = cookies.find(
        (cookie) => cookie.name === "admin-session"
      );

      // Cookie should either not exist or be expired/empty
      if (adminSessionCookie) {
        expect(adminSessionCookie.value).toBe("");
      }
    });
  });

  test.describe("Session Validation", () => {
    test("should verify session on page load", async ({ page }) => {
      await loginPage.goto();
      await loginPage.loginAsAdmin();
      await expect(page).toHaveURL("/admin");

      // Open a new tab and try to access admin
      const newPage = await page.context().newPage();
      await newPage.goto("/admin");

      // Should be able to access admin (session shared)
      await expect(newPage).toHaveURL("/admin");
      await expect(newPage.getByText("Dashboard")).toBeVisible();

      await newPage.close();
    });

    test("should handle invalid session gracefully", async ({ page }) => {
      // Set an invalid admin session cookie
      await page.context().addCookies([
        {
          name: "admin-session",
          value: "invalid-token",
          domain: "localhost",
          path: "/",
        },
      ]);

      // Try to access admin
      await page.goto("/admin");

      // Should be redirected to login
      await expect(page).toHaveURL("/admin/auth/signin");
      await expect(page.getByText("Admin Portal")).toBeVisible();
    });
  });

  test.describe("Error Handling", () => {
    test("should handle network errors during logout gracefully", async ({
      page,
    }) => {
      // Login first
      await loginPage.goto();
      await loginPage.loginAsAdmin();
      await expect(page).toHaveURL("/admin");

      // Intercept logout API call and make it fail
      await page.route("/api/admin-logout", (route) => {
        route.fulfill({ status: 500, body: "Server Error" });
      });

      // Logout should still work (fallback to client-side)
      const logoutButton = page
        .locator('[data-testid="logout-button"], button:has-text("Đăng xuất")')
        .first();
      await logoutButton.click();

      // Should still be redirected to login page (fallback behavior)
      await expect(page).toHaveURL("/admin/auth/signin");
      await expect(page.getByText("Admin Portal")).toBeVisible();
    });
  });
});
